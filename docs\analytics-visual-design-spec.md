# Analytics Page Visual Design Specification

## Overview
Based on the provided screenshots, this document outlines the complete visual structure and design patterns for rebuilding the analytics page with a modern, professional trading application interface.

## Layout Architecture

### 1. Header Navigation
- **Tab-based Navigation**: Horizontal tabs (OVERVIEW, DETAILED, OPTIONS, RISK, WINS VS LOSSES, COMPAR<PERSON>, MARKET BEHAVIOR, CALENDAR)
- **Active State**: Underlined tab with accent color
- **Secondary Controls**: Filter dropdowns and date range selectors below main tabs
- **Visual Style**: Clean, minimal tabs with subtle borders

### 2. Content Structure

#### A. Overview Tab (Primary Focus)
**Two-Column Statistics Layout:**
- **Left Column**: "YOUR STATS" section with key performance metrics
- **Right Column**: Additional metrics and time-based statistics
- **Visual Treatment**: Clean typography, right-aligned numbers, subtle separators

**Key Metrics Display:**
- Best Month, Lowest Month, Average values
- Total P&L prominently displayed
- Average Daily Volume, Average Winning Trade
- Win/Loss statistics with clear numerical presentation
- Time-based metrics (Hold Time, Trading Days, etc.)

**Chart Section:**
- **Dual Chart Layout**: Two side-by-side charts
- **Left Chart**: "DAILY NET CUMULATIVE P&L" - Line/area chart
- **Right Chart**: "NET DAILY P&L" - Bar chart
- **Visual Style**: Clean backgrounds, subtle gridlines, professional color scheme

#### B. Risk Tab Analysis
**Horizontal Bar Charts:**
- **Left Panel**: "TRADE DISTRIBUTION BY R-MULTIPLE"
- **Right Panel**: "PERFORMANCE BY R-MULTIPLE"
- **Color Coding**: Purple/blue for distribution, Green/red for performance
- **Layout**: Horizontal bars with clear labels and values

**Summary Table:**
- Clean tabular data below charts
- Alternating row colors for readability
- Right-aligned numerical values
- Clear column headers

#### C. Market Behavior Tab
**Dual Chart Analysis:**
- **Distribution Charts**: Horizontal bar charts showing market movement percentages
- **Performance Charts**: Corresponding performance metrics
- **Color Scheme**: Consistent purple/blue and green/red coding
- **Filter Controls**: Market movement and gap type filters

#### D. Calendar Tab
**Monthly Grid Layout:**
- **12-Month View**: 4x3 grid of monthly calendars
- **Color Coding**: Different colors for different performance levels
- **Navigation**: Year selector with arrow controls
- **Visual Style**: Clean grid layout with subtle borders

## Design System Elements

### Color Palette
- **Primary**: Purple/blue tones for neutral data
- **Success**: Green tones for positive performance
- **Danger**: Red tones for negative performance
- **Background**: Light gray/white for main content
- **Text**: Dark gray for primary text, lighter gray for secondary

### Typography
- **Headers**: Bold, medium-sized fonts for section titles
- **Data**: Monospace or clean sans-serif for numerical values
- **Labels**: Smaller, muted text for field labels
- **Hierarchy**: Clear size and weight differentiation

### Component Patterns

#### Statistics Cards
- **Layout**: Label on left, value on right
- **Spacing**: Consistent vertical rhythm
- **Separators**: Subtle lines between sections
- **Alignment**: Right-aligned numbers for easy scanning

#### Chart Containers
- **Background**: Clean white/light backgrounds
- **Borders**: Subtle borders around chart areas
- **Titles**: Clear chart titles with descriptive text
- **Legends**: Minimal, contextual legends

#### Data Tables
- **Headers**: Bold, left-aligned column headers
- **Rows**: Alternating background colors
- **Numbers**: Right-aligned for easy comparison
- **Spacing**: Generous padding for readability

### Interactive Elements

#### Filters and Controls
- **Dropdowns**: Clean select inputs with clear labels
- **Date Pickers**: Integrated date range selectors
- **Tabs**: Underlined active states
- **Buttons**: Minimal, functional styling

#### Responsive Behavior
- **Desktop**: Full multi-column layouts
- **Tablet**: Stacked columns, maintained chart sizes
- **Mobile**: Single column, simplified navigation

## Information Hierarchy

### Primary Level
1. **Navigation Tabs**: Most prominent, always visible
2. **Key Metrics**: Large, bold numbers for critical stats
3. **Main Charts**: Primary visual data representation

### Secondary Level
1. **Filter Controls**: Functional but not dominant
2. **Supporting Metrics**: Detailed statistics
3. **Summary Tables**: Tabular data presentation

### Tertiary Level
1. **Labels and Descriptions**: Contextual information
2. **Gridlines and Separators**: Visual organization
3. **Secondary Charts**: Supporting visualizations

## Modern UI Patterns

### Visual Enhancements
- **Subtle Shadows**: Light drop shadows on cards and containers
- **Rounded Corners**: Soft, modern corner radius on components
- **Hover States**: Subtle interactive feedback
- **Loading States**: Professional loading indicators

### Data Visualization
- **Clean Charts**: Minimal gridlines, clear data points
- **Color Consistency**: Systematic color usage across all charts
- **Responsive Charts**: Adaptive sizing for different screen sizes
- **Interactive Elements**: Hover tooltips and selection states

### Layout Principles
- **White Space**: Generous spacing between elements
- **Grid System**: Consistent alignment and proportions
- **Visual Balance**: Balanced distribution of content weight
- **Progressive Disclosure**: Logical information revelation

## Implementation Notes

### Technical Considerations
- **Chart Library**: Use Recharts for consistent chart styling
- **Responsive Design**: CSS Grid and Flexbox for layouts
- **Theme Support**: Dark/light mode compatibility
- **Performance**: Optimized rendering for large datasets

### Accessibility
- **Color Contrast**: WCAG AA compliant color combinations
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Readers**: Proper ARIA labels and descriptions
- **Focus Management**: Clear focus indicators

This specification serves as the foundation for implementing a modern, professional analytics interface that matches the visual quality and functionality shown in the reference screenshots.
