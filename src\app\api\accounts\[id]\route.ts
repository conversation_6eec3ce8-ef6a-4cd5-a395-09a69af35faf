import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { Database } from '@/types/supabase'

// PUT handler for updating an account
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { name, broker } = body;

    // Validate required fields
    if (!name || !broker) {
      return NextResponse.json({ error: 'Name and broker are required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set() {
            // API routes can't set cookies directly
          },
          remove() {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the account belongs to the user
    const { data: existingAccount, error: fetchError } = await supabase
      .from('trading_accounts')
      .select('id, name, broker')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !existingAccount) {
      return NextResponse.json({ error: 'Account not found' }, { status: 404 });
    }

    // Update the account
    const { data, error } = await supabase
      .from('trading_accounts')
      .update({
        name,
        broker,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating account:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in account update API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE handler for deleting an account
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('DELETE account API called for ID:', id);

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set() {
            // API routes can't set cookies directly
          },
          remove() {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.log('DELETE account API: Unauthorized user');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('DELETE account API: User authenticated:', user.id);

    // Verify the account belongs to the user
    const { data: existingAccount, error: fetchError } = await supabase
      .from('trading_accounts')
      .select('id, name')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !existingAccount) {
      console.log('DELETE account API: Account not found or fetch error:', fetchError);
      return NextResponse.json({ error: 'Account not found' }, { status: 404 });
    }

    console.log('DELETE account API: Found account:', existingAccount.name);

    // Check for associated trades
    const { data: trades, error: tradesError } = await supabase
      .from('trades')
      .select('id')
      .eq('account_id', id)
      .eq('user_id', user.id)
      .limit(1);

    if (tradesError) {
      console.error('DELETE account API: Error checking for trades:', tradesError);
      return NextResponse.json({ error: 'Error checking for associated trades' }, { status: 500 });
    }

    console.log('DELETE account API: Found trades count:', trades?.length || 0);

    // If there are trades, prevent deletion unless force delete is requested
    const url = new URL(request.url);
    const forceDelete = url.searchParams.get('force') === 'true';
    console.log('DELETE account API: Force delete requested:', forceDelete);

    if (trades && trades.length > 0 && !forceDelete) {
      console.log('DELETE account API: Preventing deletion due to associated trades');
      return NextResponse.json({
        error: 'Cannot delete account with associated trades',
        hasAssociatedTrades: true,
        tradeCount: trades.length
      }, { status: 400 });
    }

    // Delete the account (this will cascade delete trades due to ON DELETE CASCADE)
    console.log('DELETE account API: Proceeding with account deletion');
    const { data: deletedData, error } = await supabase
      .from('trading_accounts')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)
      .select(); // Add select to get the deleted row data

    if (error) {
      console.error('DELETE account API: Error deleting account:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Verify the deletion actually happened
    if (!deletedData || deletedData.length === 0) {
      console.error('DELETE account API: No rows were deleted');
      return NextResponse.json({ error: 'Account deletion failed - no rows affected' }, { status: 500 });
    }

    console.log('DELETE account API: Account deleted successfully, deleted rows:', deletedData.length);

    // Double-check by trying to fetch the account again
    const { data: checkAccount, error: checkError } = await supabase
      .from('trading_accounts')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .maybeSingle();

    if (checkError) {
      console.error('DELETE account API: Error checking deletion:', checkError);
    } else if (checkAccount) {
      console.error('DELETE account API: Account still exists after deletion!');
      return NextResponse.json({ error: 'Account deletion verification failed' }, { status: 500 });
    } else {
      console.log('DELETE account API: Deletion verified - account no longer exists');
    }

    return NextResponse.json({ success: true, deletedCount: deletedData.length });
  } catch (error) {
    console.error('Error in account delete API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
