"use client"

import { format, startOfDay } from "date-fns"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import {
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Legend,
  Cell,
  ReferenceLine,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface DailyCumulativePnLChartProps {
  trades: Trade[]
}

export function DailyCumulativePnLChart({ trades }: DailyCumulativePnLChartProps) {
  // Handle empty trades array
  if (!trades || trades.length === 0) {
    return (
      <div className="flex items-center justify-center h-full w-full text-muted-foreground">
        No trade data available
      </div>
    )
  }

  // Sort trades by date first to ensure chronological order
  const sortedTrades = [...trades].sort((a, b) => {
    // Handle missing time_close values
    const aTime = a.time_close ? new Date(a.time_close).getTime() : new Date().getTime()
    const bTime = b.time_close ? new Date(b.time_close).getTime() : new Date().getTime()
    return aTime - bTime
  })

  // Group trades by day and calculate daily P&L
  const dailyPnL = sortedTrades.reduce((acc: { [key: string]: number }, trade) => {
    if (!trade.time_close) return acc
    try {
      const day = format(startOfDay(new Date(trade.time_close)), "yyyy-MM-dd")
      acc[day] = (acc[day] || 0) + (typeof trade.profit === 'number' ? trade.profit : 0)
      return acc
    } catch (e) {
      console.warn('Invalid date in trade:', trade)
      return acc
    }
  }, {})

  // Convert to array and sort by date
  const sortedDays = Object.entries(dailyPnL)
    .map(([date, pnl]) => ({
      date,
      pnl: parseFloat(pnl.toFixed(2)), // Limit decimal places
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) // Ensure chronological order

  // Calculate cumulative P&L starting from 0
  let cumulativePnL = 0
  const chartData = [
    // Add initial point at 0
    {
      date: sortedDays.length > 0 ? sortedDays[0].date : format(new Date(), "yyyy-MM-dd"),
      pnl: 0,
      cumulativePnL: 0
    },
    // Add all other points
    ...sortedDays.map(({ date, pnl }) => {
      cumulativePnL += pnl
      return {
        date,
        pnl,
        cumulativePnL: parseFloat(cumulativePnL.toFixed(2)), // Limit decimal places
      }
    })
  ] // Show all days

  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart data={chartData} margin={{ top: 20, right: 30, bottom: 40, left: 20 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.4} />
        <XAxis
          dataKey="date"
          tickFormatter={(value: string) => format(new Date(value), "MMM d")}
          stroke="hsl(var(--muted-foreground))"
          tick={{
            fontSize: 11,
            fill: 'hsl(var(--muted-foreground))',
            opacity: 0.7,
            textAnchor: 'end',
            dy: 10,
            dx: -10
          }}
          height={60}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <YAxis
          yAxisId="left"
          orientation="left"
          tickFormatter={(value: number) => `$${value.toLocaleString()}`}
          stroke="hsl(var(--muted-foreground))"
          tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))', opacity: 0.7 }}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <ReferenceLine
          y={0}
          yAxisId="left"
          stroke="rgba(100, 100, 100, 0.5)"
          strokeDasharray="3 3"
        />
        <Tooltip
          content={({ active, payload }: { active?: boolean, payload?: any[] }) => (
            <ChartTooltip active={active} payload={payload}
              labelFormatter={(value: string) => format(new Date(value), "MMM d, yyyy")}
              formatter={(value: any, name: string) => {
                if (name === "Daily P&L") return `$${value.toLocaleString()}`
                if (name === "Cumulative P&L") return `$${value.toLocaleString()}`
                return value
              }}
            />
          )}
        />
        <Legend />
        <Bar
          yAxisId="left"
          dataKey="pnl"
          name="Daily P&L"
        >
          {chartData.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={entry.pnl >= 0 ? "#10b981" : "#ef4444"}
            />
          ))}
        </Bar>
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="cumulativePnL"
          name="Cumulative P&L"
          stroke="#2563eb"
          strokeWidth={2}
          dot={{ r: 3 }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}
