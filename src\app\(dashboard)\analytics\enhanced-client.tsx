"use client"

import { useState, useMemo } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BarChart3,
  Clock,
  ShieldAlert,
  Timer,
  TrendingDown,
  TrendingUp,
  Zap,
  DollarSign,
  Target,
  Activity,
  PieChart,
  BarChart,
  ArrowUpRight,
  ArrowDownRight,
  Info
} from "lucide-react"
import { Trade } from "@/types/trade"
import { useAccount } from "@/contexts/account-context"
import { toast } from "sonner"
import { useAnalyticsData } from "@/hooks/use-analytics-data"
import { cn } from "@/lib/utils"

// Import existing analytics components
import { SymbolPerformance } from "@/components/analytics/symbol-performance"
import { TimeOfDayAnalysis } from "@/components/analytics/time-of-day-analysis"
import { TradeDurationAnalysis } from "@/components/analytics/trade-duration-analysis"
import { DrawdownAnalysis } from "@/components/analytics/drawdown-analysis"
import { ConsecutiveTradesAnalysis } from "@/components/analytics/consecutive-trades-analysis"
import { RiskManagementMetrics } from "@/components/analytics/risk-management-metrics"
import { AnalyticsLoadingCentered } from "@/components/analytics/analytics-loading"

interface EnhancedAnalyticsClientProps {
  userId: string;
  initialTrades: Trade[];
  initialSummary: any | null;
  selectedAccountId: string | null;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon: React.ReactNode;
  description?: string;
  trend?: "up" | "down" | "neutral";
  format?: "currency" | "percentage" | "number";
}

function MetricCard({ 
  title, 
  value, 
  change, 
  changeLabel, 
  icon, 
  description, 
  trend = "neutral",
  format = "number" 
}: MetricCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === "string") return val;
    
    switch (format) {
      case "currency":
        return `$${val.toFixed(2)}`;
      case "percentage":
        return `${val.toFixed(2)}%`;
      default:
        return val.toString();
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case "up":
        return "text-emerald-500";
      case "down":
        return "text-rose-500";
      default:
        return "text-muted-foreground";
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <ArrowUpRight className="h-3 w-3" />;
      case "down":
        return <ArrowDownRight className="h-3 w-3" />;
      default:
        return null;
    }
  };

  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {formatValue(value)}
        </div>
        {change !== undefined && (
          <div className={cn("flex items-center text-xs", getTrendColor())}>
            {getTrendIcon()}
            <span className="ml-1">
              {change > 0 ? "+" : ""}{change.toFixed(2)}% {changeLabel}
            </span>
          </div>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

export default function EnhancedAnalyticsClient({
  initialTrades,
  initialSummary
}: EnhancedAnalyticsClientProps) {
  const router = useRouter()
  const [activeSection, setActiveSection] = useState("overview")
  const { selectedAccountId } = useAccount()

  // Use React Query to fetch analytics data
  const {
    data: analyticsData,
    isLoading,
    error
  } = useAnalyticsData(selectedAccountId)

  // Extract trades and initial balance from query result
  const trades = analyticsData?.trades || initialTrades
  const initialBalance = analyticsData?.summary?.initial_balance ||
    initialSummary?.initial_balance || 10000

  // Calculate comprehensive metrics
  const metrics = useMemo(() => {
    if (!trades || trades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalProfit: 0,
        averageProfit: 0,
        bestTrade: 0,
        worstTrade: 0,
        profitFactor: 0,
        sharpeRatio: 0,
        maxDrawdown: 0,
        currentBalance: initialBalance,
        returnOnInvestment: 0,
        averageWin: 0,
        averageLoss: 0,
        largestWinStreak: 0,
        largestLossStreak: 0,
        expectancy: 0
      };
    }

    const totalTrades = trades.length;
    const winningTrades = trades.filter(t => t.profit > 0).length;
    const losingTrades = trades.filter(t => t.profit < 0).length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    
    const totalProfit = trades.reduce((sum, t) => sum + t.profit, 0);
    const averageProfit = totalTrades > 0 ? totalProfit / totalTrades : 0;
    
    const profits = trades.map(t => t.profit);
    const bestTrade = Math.max(...profits);
    const worstTrade = Math.min(...profits);
    
    const grossProfit = trades.filter(t => t.profit > 0).reduce((sum, t) => sum + t.profit, 0);
    const grossLoss = Math.abs(trades.filter(t => t.profit < 0).reduce((sum, t) => sum + t.profit, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;
    
    const currentBalance = initialBalance + totalProfit;
    const returnOnInvestment = initialBalance > 0 ? (totalProfit / initialBalance) * 100 : 0;
    
    const averageWin = winningTrades > 0 ? grossProfit / winningTrades : 0;
    const averageLoss = losingTrades > 0 ? grossLoss / losingTrades : 0;
    
    // Calculate expectancy
    const expectancy = (winRate / 100) * averageWin - ((100 - winRate) / 100) * averageLoss;

    return {
      totalTrades,
      winningTrades,
      losingTrades,
      winRate,
      totalProfit,
      averageProfit,
      bestTrade,
      worstTrade,
      profitFactor,
      sharpeRatio: 0, // TODO: Calculate Sharpe ratio
      maxDrawdown: 0, // TODO: Calculate max drawdown
      currentBalance,
      returnOnInvestment,
      averageWin,
      averageLoss,
      largestWinStreak: 0, // TODO: Calculate streaks
      largestLossStreak: 0,
      expectancy
    };
  }, [trades, initialBalance]);

  // Show error toast if query fails
  if (error) {
    console.error("Error fetching analytics data:", error)
    toast.error("Failed to load analytics data")
  }

  if (isLoading) {
    return <AnalyticsLoadingCentered />
  }

  if (selectedAccountId === null) {
    return (
      <div className="container py-6 space-y-6">
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-primary" />
            </div>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your analytics. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">
      {/* Header Section */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
        <p className="text-muted-foreground">
          Comprehensive analysis of your trading performance and risk metrics
        </p>
      </div>

      {/* Navigation */}
      <Tabs value={activeSection} onValueChange={setActiveSection} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7 lg:w-fit">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="symbols" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Symbols
          </TabsTrigger>
          <TabsTrigger value="timing" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Timing
          </TabsTrigger>
          <TabsTrigger value="duration" className="flex items-center gap-2">
            <Timer className="h-4 w-4" />
            Duration
          </TabsTrigger>
          <TabsTrigger value="drawdown" className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            Drawdown
          </TabsTrigger>
          <TabsTrigger value="streaks" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Streaks
          </TabsTrigger>
          <TabsTrigger value="risk" className="flex items-center gap-2">
            <ShieldAlert className="h-4 w-4" />
            Risk
          </TabsTrigger>
        </TabsList>

        {/* Overview Section */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Total Profit/Loss"
              value={metrics.totalProfit}
              format="currency"
              icon={<DollarSign className="h-4 w-4" />}
              trend={metrics.totalProfit >= 0 ? "up" : "down"}
              description="Net profit from all trades"
            />
            <MetricCard
              title="Win Rate"
              value={metrics.winRate}
              format="percentage"
              icon={<Target className="h-4 w-4" />}
              trend={metrics.winRate >= 50 ? "up" : "down"}
              description={`${metrics.winningTrades} wins, ${metrics.losingTrades} losses`}
            />
            <MetricCard
              title="Total Trades"
              value={metrics.totalTrades}
              icon={<Activity className="h-4 w-4" />}
              description="Total number of completed trades"
            />
            <MetricCard
              title="Return on Investment"
              value={metrics.returnOnInvestment}
              format="percentage"
              icon={<TrendingUp className="h-4 w-4" />}
              trend={metrics.returnOnInvestment >= 0 ? "up" : "down"}
              description="ROI based on initial balance"
            />
          </div>

          {/* Performance Summary Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Trading Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart className="h-5 w-5 text-primary" />
                  Trading Performance
                </CardTitle>
                <CardDescription>
                  Key metrics and performance indicators
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Profit</span>
                      <span className={cn(
                        "text-sm font-medium",
                        metrics.averageProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${metrics.averageProfit.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Best Trade</span>
                      <span className="text-sm font-medium text-emerald-500">
                        ${metrics.bestTrade.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Worst Trade</span>
                      <span className="text-sm font-medium text-rose-500">
                        ${metrics.worstTrade.toFixed(2)}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Profit Factor</span>
                      <span className="text-sm font-medium">
                        {metrics.profitFactor === Infinity ? "∞" : metrics.profitFactor.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Win</span>
                      <span className="text-sm font-medium text-emerald-500">
                        ${metrics.averageWin.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Loss</span>
                      <span className="text-sm font-medium text-rose-500">
                        ${metrics.averageLoss.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Expectancy</span>
                  <span className={cn(
                    "text-sm font-medium",
                    metrics.expectancy >= 0 ? "text-emerald-500" : "text-rose-500"
                  )}>
                    ${metrics.expectancy.toFixed(2)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Account Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5 text-primary" />
                  Account Summary
                </CardTitle>
                <CardDescription>
                  Current account status and balance information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Initial Balance</span>
                    <span className="text-sm font-medium">
                      ${initialBalance.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Current Balance</span>
                    <span className={cn(
                      "text-sm font-medium",
                      metrics.currentBalance >= initialBalance ? "text-emerald-500" : "text-rose-500"
                    )}>
                      ${metrics.currentBalance.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Net Change</span>
                    <span className={cn(
                      "text-sm font-medium",
                      metrics.totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                    )}>
                      {metrics.totalProfit >= 0 ? "+" : ""}${metrics.totalProfit.toFixed(2)}
                    </span>
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Win/Loss Ratio</span>
                    <span className="text-sm font-medium">
                      {metrics.averageLoss > 0 ? (metrics.averageWin / metrics.averageLoss).toFixed(2) : "∞"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Risk-Reward Ratio</span>
                    <span className="text-sm font-medium">
                      1:{metrics.averageWin > 0 && metrics.averageLoss > 0 ? (metrics.averageWin / metrics.averageLoss).toFixed(2) : "N/A"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-primary" />
                Quick Insights
              </CardTitle>
              <CardDescription>
                Key takeaways from your trading performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50">
                  <div className={cn(
                    "h-2 w-2 rounded-full mt-2",
                    metrics.winRate >= 50 ? "bg-emerald-500" : "bg-rose-500"
                  )} />
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Win Rate Performance</p>
                    <p className="text-xs text-muted-foreground">
                      {metrics.winRate >= 50
                        ? `Strong win rate of ${metrics.winRate.toFixed(1)}% indicates good trade selection`
                        : `Win rate of ${metrics.winRate.toFixed(1)}% suggests room for improvement in trade selection`
                      }
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50">
                  <div className={cn(
                    "h-2 w-2 rounded-full mt-2",
                    metrics.profitFactor >= 1.5 ? "bg-emerald-500" : metrics.profitFactor >= 1 ? "bg-yellow-500" : "bg-rose-500"
                  )} />
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Profit Factor</p>
                    <p className="text-xs text-muted-foreground">
                      {metrics.profitFactor >= 1.5
                        ? "Excellent profit factor indicates strong risk management"
                        : metrics.profitFactor >= 1
                        ? "Decent profit factor, consider optimizing risk management"
                        : "Low profit factor suggests need for better risk management"
                      }
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50">
                  <div className={cn(
                    "h-2 w-2 rounded-full mt-2",
                    metrics.expectancy >= 0 ? "bg-emerald-500" : "bg-rose-500"
                  )} />
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Expectancy</p>
                    <p className="text-xs text-muted-foreground">
                      {metrics.expectancy >= 0
                        ? `Positive expectancy of $${metrics.expectancy.toFixed(2)} per trade is favorable`
                        : `Negative expectancy suggests strategy refinement needed`
                      }
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Symbols Section */}
        <TabsContent value="symbols">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                Symbol Performance Analysis
              </CardTitle>
              <CardDescription>
                Compare performance across different currency pairs and instruments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SymbolPerformance trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Timing Section */}
        <TabsContent value="timing">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Time of Day Analysis
              </CardTitle>
              <CardDescription>
                Analyze how your trading performance varies throughout the day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeOfDayAnalysis trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Duration Section */}
        <TabsContent value="duration">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Timer className="h-5 w-5 text-primary" />
                Trade Duration Analysis
              </CardTitle>
              <CardDescription>
                Understand how trade duration affects your performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TradeDurationAnalysis trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Drawdown Section */}
        <TabsContent value="drawdown">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5 text-primary" />
                Drawdown Analysis
              </CardTitle>
              <CardDescription>
                Track your historical drawdowns and recovery periods
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DrawdownAnalysis trades={trades} initialBalance={initialBalance} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Streaks Section */}
        <TabsContent value="streaks">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                Consecutive Trades Analysis
              </CardTitle>
              <CardDescription>
                Analyze winning and losing streaks and their impact
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ConsecutiveTradesAnalysis trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Risk Section */}
        <TabsContent value="risk">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldAlert className="h-5 w-5 text-primary" />
                Risk Management Metrics
              </CardTitle>
              <CardDescription>
                Evaluate your risk management with advanced metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RiskManagementMetrics trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
