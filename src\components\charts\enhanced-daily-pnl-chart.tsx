"use client"

import { format, startOfDay } from "date-fns"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import { ChartContainer } from "@/components/ui/chart-container"
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
  ReferenceLine,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface DailyPnLChartProps {
  trades: Trade[]
}

export function EnhancedDailyPnLChart({ trades }: DailyPnLChartProps) {
  // Group trades by day and sum profits
  const dailyPnL = trades.reduce((acc, trade) => {
    // Convert to local date string (YYYY-MM-DD)
    const day = startOfDay(new Date(trade.time_close)).toISOString().split('T')[0]
    acc[day] = (acc[day] || 0) + trade.profit
    return acc
  }, {} as Record<string, number>)

  // Convert to array and sort by date
  const chartData = Object.entries(dailyPnL)
    .map(([date, pnl]) => ({
      date,
      pnl: parseFloat(pnl.toFixed(2)), // Limit decimal places
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) // Ensure chronological order

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} margin={{ top: 20, right: 20, bottom: 40, left: 60 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.4} />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => format(new Date(value), "MMM d")}
            stroke="hsl(var(--muted-foreground))"
            tick={{
              fontSize: 11,
              fill: 'hsl(var(--muted-foreground))',
              opacity: 0.7,
              textAnchor: 'end'
            }}
            height={60}
            axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
          />
          <YAxis
            tickFormatter={(value) => `$${value.toLocaleString()}`}
            stroke="hsl(var(--muted-foreground))"
            tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))', opacity: 0.7 }}
            axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
          />
          <ReferenceLine
            y={0}
            stroke="rgba(100, 100, 100, 0.5)"
            strokeDasharray="3 3"
          />
          <Tooltip
            content={({ active, payload }) => (
              <ChartTooltip active={active} payload={payload}
                labelFormatter={(value) => format(new Date(value), "MMM d, yyyy")}
                formatter={(value) => `$${value.toLocaleString()}`}
              />
            )}
          />
          <Bar
            dataKey="pnl"
            animationDuration={1000}
            animationEasing="ease-out"
          >
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.pnl >= 0 ? "#10b981" : "#ef4444"}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
