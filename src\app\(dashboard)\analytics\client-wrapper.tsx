"use client"

import dynamic from 'next/dynamic';
import { Trade } from '@/types/trade';

// Dynamically import the modern analytics component with no SSR
const ModernAnalytics = dynamic(() => import('./modern-analytics'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialTrades: Trade[];
  initialSummary: any | null;
  selectedAccountId: string | null;
}

export default function ClientWrapper({
  userId,
  initialTrades,
  initialSummary,
  selectedAccountId
}: ClientWrapperProps) {
  return (
    <ModernAnalytics
      initialTrades={initialTrades}
      initialSummary={initialSummary}
    />
  );
}
