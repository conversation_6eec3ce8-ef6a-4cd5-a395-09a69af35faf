"use client"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import { MobileNav } from "./mobile-nav"
import { useEffect, useState } from "react"
import { KeyboardShortcuts, defaultShortcuts, useKeyboardShortcuts } from "@/components/ui/keyboard-shortcuts"
import { NavigationLoading } from "@/components/navigation-loading"
import { NavigationEvents } from "@/components/navigation-events"
import {
  BarChart,
  BookOpen,
  Calendar,
  GraduationCap,
  LayoutDashboard,
  List,
  Plus,
  Settings,
  LineChart,
  UserCircle,
  TrendingUp,
  Target,
  BookMarked,
  Keyboard,
  Wallet,
  Notebook,
  ChevronLeft,
  ChevronRight,
  Menu,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useSidebar } from "@/contexts/sidebar-context"
import { AccountSwitcher } from "@/components/account-switcher"

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
}

export const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Trade Log",
    href: "/trades",
    icon: List,
  },
  {
    title: "Symbols",
    href: "/symbols",
    icon: LineChart,
  },
  {
    title: "Daily Journal",
    href: "/journal",
    icon: BookOpen,
  },
  {
    title: "Notebook",
    href: "/notebook",
    icon: Notebook,
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: TrendingUp,
  },
  {
    title: "Metrics & Goals",
    href: "/metrics-goals",
    icon: Target,
  },
  {
    title: "Playbook",
    href: "/playbook",
    icon: BookMarked,
  },
  {
    title: "Accounts",
    href: "/accounts",
    icon: Wallet,
  },
  {
    title: "Profile",
    href: "/profile",
    icon: UserCircle,
  },
]

// Export navItems for use in mobile-nav

interface DashboardLayoutProps {
  children: React.ReactNode
  pageTitle?: string
}

export function DashboardLayout({ children, pageTitle }: DashboardLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { showShortcuts, setShowShortcuts } = useKeyboardShortcuts()
  const { isSidebarCollapsed, toggleSidebar } = useSidebar()

  // Handle keyboard navigation shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only trigger if not in an input, textarea, or other form element
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement ||
        // Check for editor content areas
        (e.target instanceof HTMLElement &&
          (e.target.classList.contains('ProseMirror') ||
           e.target.closest('.ProseMirror') ||
           e.target.closest('.simple-editor-content') ||
           e.target.closest('.tiptap-editor') ||
           e.target.closest('.content-wrapper')))
      ) {
        return
      }

      // Toggle sidebar with Alt+S
      if (e.altKey && e.key === 's') {
        e.preventDefault()
        toggleSidebar()
        return
      }

      // Navigation shortcuts with 'g' prefix
      if (e.key === 'g' && !e.ctrlKey && !e.metaKey) {
        // Wait for the next key
        const handleSecondKey = (e2: KeyboardEvent) => {
          // Don't trigger if user is now in an editor
          if (
            e2.target instanceof HTMLElement &&
            (e2.target.classList.contains('ProseMirror') ||
             e2.target.closest('.ProseMirror') ||
             e2.target.closest('.simple-editor-content') ||
             e2.target.closest('.tiptap-editor') ||
             e2.target.closest('.content-wrapper'))
          ) {
            return
          }

          switch (e2.key) {
            case 'd': router.push('/dashboard'); break
            case 't': router.push('/trades'); break
            case 's': router.push('/symbols'); break
            case 'j': router.push('/journal'); break
            case 'n': router.push('/notebook'); break
            case 'a': router.push('/analytics'); break
            case 'm': router.push('/metrics-goals'); break
            case 'p': router.push('/playbook'); break
            case 'w': router.push('/accounts'); break
          }
          window.removeEventListener('keydown', handleSecondKey)
        }

        // Listen for the second key
        window.addEventListener('keydown', handleSecondKey, { once: true })
      }

      // Direct shortcuts - DISABLED for now to prevent conflicts with editor
      // if (!e.ctrlKey && !e.metaKey && !e.shiftKey) {
      //   switch (e.key) {
      //     case 'n': router.push('/add-trade'); break
      //   }
      // }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [router])

  return (
    <div className="flex min-h-screen flex-col bg-background md:flex-row">
      {/* Navigation loading indicator */}
      <NavigationLoading />
      <NavigationEvents />

      {/* Keyboard Shortcuts Dialog */}
      <KeyboardShortcuts
        shortcuts={defaultShortcuts}
        isOpen={showShortcuts}
        onClose={() => setShowShortcuts(false)}
      />

      {/* Mobile Header */}
      <header className="sticky top-0 z-50 flex h-14 items-center border-b bg-card px-4 md:hidden">
        <MobileNav />
        <span className="ml-2 text-lg font-semibold">TradePivot</span>
        <div className="ml-auto flex items-center gap-2">
          <AccountSwitcher variant="ghost" size="sm" className="min-w-[100px] max-w-[140px]" />
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowShortcuts(true)}
            className="h-8 w-8"
          >
            <Keyboard className="h-4 w-4" />
          </Button>
          <ModeToggle />
        </div>
      </header>

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-0 z-40 hidden h-screen border-r bg-card md:block transition-all duration-300",
          isSidebarCollapsed ? "w-16" : "w-64"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Logo and Toggle Button */}
          <div className="flex h-14 items-center justify-between border-b px-4">
            {!isSidebarCollapsed && (
              <span className="text-lg font-semibold">TradePivot</span>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className={cn(
                "h-8 w-8",
                isSidebarCollapsed ? "mx-auto" : "ml-auto"
              )}
              aria-label={isSidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {isSidebarCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Add Trade Button */}
          <div className={cn("p-4", isSidebarCollapsed && "px-2")}>
            <Link href="/add-trade">
              <Button
                size={isSidebarCollapsed ? undefined : "sm"}
                className={cn(
                  "bg-primary hover:bg-primary/90",
                  isSidebarCollapsed ? "w-full p-2 justify-center" : "w-full"
                )}
              >
                <Plus className={cn("h-4 w-4", !isSidebarCollapsed && "mr-2")} />
                {!isSidebarCollapsed && "Add Trade"}
              </Button>
            </Link>
          </div>

          {/* Navigation */}
          <nav className={cn(
            "flex-1 space-y-1 py-4",
            isSidebarCollapsed ? "px-2" : "px-2"
          )}>
            {navItems.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center rounded-lg py-2 text-sm font-medium transition-colors",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                    isSidebarCollapsed
                      ? "px-2 justify-center"
                      : "px-3"
                  )}
                  title={isSidebarCollapsed ? item.title : undefined}
                >
                  <item.icon className={cn(
                    "h-4 w-4",
                    !isSidebarCollapsed && "mr-2"
                  )} />
                  {!isSidebarCollapsed && item.title}
                </Link>
              )
            })}
          </nav>

          {/* Bottom section */}
          <div className="border-t p-4">
            <div className={cn(
              "flex items-center",
              isSidebarCollapsed ? "justify-center" : "justify-between"
            )}>
              {!isSidebarCollapsed && (
                <div className="flex items-center gap-2">
                  <Link href="/profile">
                    <Button variant="ghost" size="icon">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowShortcuts(true)}
                  >
                    <Keyboard className="h-4 w-4" />
                  </Button>
                </div>
              )}
              <ModeToggle />
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <main className={cn(
        "flex-1 transition-all duration-300",
        isSidebarCollapsed ? "md:ml-16" : "md:ml-64"
      )}>
        {/* Desktop Header with Account Switcher */}
        <header className="sticky top-0 z-40 hidden h-14 items-center justify-between border-b bg-card px-6 md:flex">
          <div className="flex items-center gap-4">
            {/* Page Title */}
            <h1 className="text-xl font-semibold">{pageTitle || "TradePivot"}</h1>
          </div>

          <div className="flex items-center gap-2">
            <AccountSwitcher variant="outline" size="sm" className="min-w-[180px]" />
          </div>
        </header>

        <div className="container mx-auto max-w-[1600px] p-4 md:p-6">
          {children}
        </div>
      </main>
    </div>
  )
}