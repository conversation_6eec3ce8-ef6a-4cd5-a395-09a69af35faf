"use client"

import { cn } from "@/lib/utils"

interface DualColorGaugeProps {
  leftValue: number
  rightValue: number
  size?: number
  strokeWidth?: number
  className?: string
  leftColor?: string
  rightColor?: string
  backgroundColor?: string
  showLabels?: boolean
  leftLabel?: string
  rightLabel?: string
}

export function DualColorGauge({
  leftValue,
  rightValue,
  size = 60,
  strokeWidth = 6,
  className,
  leftColor = "stroke-emerald-500",
  rightColor = "stroke-rose-500",
  backgroundColor = "stroke-muted",
  showLabels = false,
  leftLabel = "",
  rightLabel = ""
}: DualColorGaugeProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * Math.PI
  
  // Calculate total and percentages
  const total = leftValue + rightValue
  const leftPercentage = total > 0 ? leftValue / total : 0
  const rightPercentage = total > 0 ? rightValue / total : 0
  
  // Calculate stroke-dasharray and stroke-dashoffset for both segments
  const leftDashArray = `${circumference * leftPercentage} ${circumference * (1 - leftPercentage)}`
  const rightDashArray = `${circumference * rightPercentage} ${circumference * (1 - rightPercentage)}`
  const rightDashOffset = -circumference * leftPercentage // Start right after left segment

  return (
    <div className={cn("relative flex flex-col items-center", className)}>
      <svg
        width={size}
        height={size / 2 + strokeWidth}
        viewBox={`0 0 ${size} ${size / 2 + strokeWidth}`}
      >
        {/* Background semi-circle */}
        <path
          d={`M ${strokeWidth / 2} ${size / 2}
              a ${radius} ${radius} 0 0 1 ${size - strokeWidth} 0`}
          fill="none"
          strokeWidth={strokeWidth}
          className={backgroundColor}
          strokeLinecap="round"
        />

        {/* Left segment (win) */}
        <path
          d={`M ${strokeWidth / 2} ${size / 2}
              a ${radius} ${radius} 0 0 1 ${size - strokeWidth} 0`}
          fill="none"
          strokeWidth={strokeWidth}
          className={leftColor}
          strokeLinecap="butt"
          strokeDasharray={leftDashArray}
          strokeDashoffset="0"
        />

        {/* Right segment (loss) */}
        <path
          d={`M ${strokeWidth / 2} ${size / 2}
              a ${radius} ${radius} 0 0 1 ${size - strokeWidth} 0`}
          fill="none"
          strokeWidth={strokeWidth}
          className={rightColor}
          strokeLinecap="butt"
          strokeDasharray={rightDashArray}
          strokeDashoffset={rightDashOffset.toString()}
        />
      </svg>

      {/* Labels */}
      {showLabels && (
        <div className="absolute w-full flex justify-between bottom-[-12px] text-[9px] text-muted-foreground">
          <span className="ml-0">{leftLabel}</span>
          <span className="mr-0">{rightLabel}</span>
        </div>
      )}
    </div>
  )
}
