export interface CustomMetric {
  id: string;
  user_id: string;
  name: string;
  description?: string | null;
  formula: string;
  is_percentage: boolean;
  display_precision: number;
  is_higher_better: boolean;
  target_value?: number | null;
  created_at: string;
  updated_at: string;
}

export interface Goal {
  id: string;
  user_id: string;
  title: string;
  description?: string | null;
  metric_id?: string | null;
  target_value: number;
  current_value: number;
  start_date: string;
  end_date: string;
  is_completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface MetricVariable {
  name: string;
  description: string;
  example: string;
}

export interface MetricOperator {
  symbol: string;
  description: string;
  example: string;
}

// Available variables for metric formulas
export const METRIC_VARIABLES: MetricVariable[] = [
  { name: 'win_count', description: 'Number of winning trades', example: 'win_count' },
  { name: 'loss_count', description: 'Number of losing trades', example: 'loss_count' },
  { name: 'total_trades', description: 'Total number of trades', example: 'total_trades' },
  { name: 'win_rate', description: 'Win rate as a decimal', example: 'win_rate' },
  { name: 'profit_sum', description: 'Sum of all profits', example: 'profit_sum' },
  { name: 'loss_sum', description: 'Sum of all losses (as a positive number)', example: 'loss_sum' },
  { name: 'net_profit', description: 'Net profit (profit_sum - loss_sum)', example: 'net_profit' },
  { name: 'avg_win', description: 'Average winning trade', example: 'avg_win' },
  { name: 'avg_loss', description: 'Average losing trade (as a positive number)', example: 'avg_loss' },
  { name: 'max_win', description: 'Largest winning trade', example: 'max_win' },
  { name: 'max_loss', description: 'Largest losing trade (as a positive number)', example: 'max_loss' },
  { name: 'avg_trade', description: 'Average trade result', example: 'avg_trade' },
  { name: 'max_drawdown', description: 'Maximum drawdown amount', example: 'max_drawdown' },
  { name: 'max_drawdown_pct', description: 'Maximum drawdown percentage', example: 'max_drawdown_pct' },
  { name: 'avg_trade_duration', description: 'Average trade duration in minutes', example: 'avg_trade_duration' },
];

// Available operators for metric formulas
export const METRIC_OPERATORS: MetricOperator[] = [
  { symbol: '+', description: 'Addition', example: 'win_count + loss_count' },
  { symbol: '-', description: 'Subtraction', example: 'profit_sum - loss_sum' },
  { symbol: '*', description: 'Multiplication', example: 'win_rate * 100' },
  { symbol: '/', description: 'Division', example: 'profit_sum / total_trades' },
  { symbol: '()', description: 'Parentheses for grouping', example: '(win_count / total_trades) * 100' },
  { symbol: 'Math.abs()', description: 'Absolute value', example: 'Math.abs(net_profit)' },
  { symbol: 'Math.max()', description: 'Maximum value', example: 'Math.max(win_rate, 0.5)' },
  { symbol: 'Math.min()', description: 'Minimum value', example: 'Math.min(loss_sum, 1000)' },
  { symbol: 'Math.round()', description: 'Round to nearest integer', example: 'Math.round(win_rate * 100)' },
  { symbol: 'Math.sqrt()', description: 'Square root', example: 'Math.sqrt(total_trades)' },
];
