"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BarChart3, 
  DollarSign, 
  Hash, 
  Percent as PercentIcon, 
  Pie<PERSON>hart as PieChartIcon, 
  Scale, 
  TrendingUp,
  TrendingDown,
  Target,
  Activity
} from "lucide-react"
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, ReferenceLine
} from "recharts"
import { PieChart, Pie, Sector } from "recharts"
import { cn } from "@/lib/utils"
import { subDays, subMonths, isAfter } from "date-fns"

interface EnhancedSymbolPerformanceProps {
  trades: Trade[]
}

type MetricType = "profit" | "winRate" | "tradeCount" | "averageProfit" | "profitFactor"
type ChartType = "bar" | "pie"
type TimeRange = "all" | "1m" | "3m" | "6m" | "1y"

export function EnhancedSymbolPerformance({ trades }: EnhancedSymbolPerformanceProps) {
  const [selectedMetric, setSelectedMetric] = useState<MetricType>("profit")
  const [chartType, setChartType] = useState<ChartType>("bar")
  const [timeRange, setTimeRange] = useState<TimeRange>("all")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [activeIndex, setActiveIndex] = useState(0)

  // Filter trades by time range
  const filteredTrades = useMemo(() => {
    if (timeRange === "all") return trades

    const now = new Date()
    let cutoffDate: Date

    switch (timeRange) {
      case "1m":
        cutoffDate = subMonths(now, 1)
        break
      case "3m":
        cutoffDate = subMonths(now, 3)
        break
      case "6m":
        cutoffDate = subMonths(now, 6)
        break
      case "1y":
        cutoffDate = subMonths(now, 12)
        break
      default:
        return trades
    }

    return trades.filter(trade => isAfter(new Date(trade.time_close), cutoffDate))
  }, [trades, timeRange])

  // Group trades by symbol and calculate metrics
  const symbolData = useMemo(() => {
    const symbolMap = new Map<string, {
      symbol: string
      totalProfit: number
      wins: number
      losses: number
      tradeCount: number
      totalVolume: number
    }>()

    filteredTrades.forEach(trade => {
      const symbol = trade.symbol
      const isWin = trade.profit > 0

      if (!symbolMap.has(symbol)) {
        symbolMap.set(symbol, {
          symbol,
          totalProfit: 0,
          wins: 0,
          losses: 0,
          tradeCount: 0,
          totalVolume: 0
        })
      }

      const data = symbolMap.get(symbol)!
      data.totalProfit += trade.profit
      data.tradeCount += 1
      data.totalVolume += trade.volume

      if (isWin) {
        data.wins += 1
      } else {
        data.losses += 1
      }
    })

    // Convert map to array and calculate additional metrics
    return Array.from(symbolMap.values())
      .map(data => ({
        ...data,
        winRate: data.tradeCount > 0 ? (data.wins / data.tradeCount) * 100 : 0,
        averageProfit: data.tradeCount > 0 ? data.totalProfit / data.tradeCount : 0,
        profitFactor: (() => {
          const grossProfit = filteredTrades
            .filter(t => t.symbol === data.symbol && t.profit > 0)
            .reduce((sum, t) => sum + t.profit, 0);

          const grossLoss = Math.abs(filteredTrades
            .filter(t => t.symbol === data.symbol && t.profit < 0)
            .reduce((sum, t) => sum + t.profit, 0));

          return grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;
        })()
      }))
      .sort((a, b) => {
        const aValue = getMetricValue(a, selectedMetric);
        const bValue = getMetricValue(b, selectedMetric);
        return sortOrder === "desc" ? bValue - aValue : aValue - bValue;
      })
  }, [filteredTrades, selectedMetric, sortOrder])

  // Helper functions
  const getMetricValue = (data: any, metric: MetricType): number => {
    switch (metric) {
      case "profit":
        return data.totalProfit
      case "winRate":
        return data.winRate
      case "tradeCount":
        return data.tradeCount
      case "averageProfit":
        return data.averageProfit
      case "profitFactor":
        return data.profitFactor === Infinity ? 999999 : data.profitFactor
      default:
        return 0
    }
  }

  const getMetricLabel = (metric: MetricType): string => {
    switch (metric) {
      case "profit":
        return "Total Profit"
      case "winRate":
        return "Win Rate (%)"
      case "tradeCount":
        return "Trade Count"
      case "averageProfit":
        return "Average Profit"
      case "profitFactor":
        return "Profit Factor"
      default:
        return ""
    }
  }

  const formatMetricValue = (value: number, metric: MetricType): string => {
    switch (metric) {
      case "profit":
      case "averageProfit":
        return `$${value.toFixed(2)}`
      case "winRate":
        return `${value.toFixed(1)}%`
      case "profitFactor":
        return value === Infinity ? "∞" : value.toFixed(2)
      case "tradeCount":
        return Math.round(value).toString()
      default:
        return value.toFixed(2)
    }
  }

  // Chart data preparation
  const chartData = useMemo(() => {
    return symbolData.map(data => ({
      symbol: data.symbol,
      totalProfit: data.totalProfit,
      winRate: data.winRate,
      tradeCount: data.tradeCount,
      averageProfit: data.averageProfit,
      profitFactor: data.profitFactor === Infinity ? 999999 : data.profitFactor,
      wins: data.wins,
      losses: data.losses
    }));
  }, [symbolData])

  // Get colors for the bars/pie slices
  const getBarColor = (data: any) => {
    const value = getMetricValue(data, selectedMetric);
    if (selectedMetric === "profit" || selectedMetric === "averageProfit") {
      return value >= 0 ? "hsl(var(--chart-2))" : "hsl(var(--chart-1))";
    }
    return "hsl(var(--primary))";
  }

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium text-foreground">{label}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">
              <span className="text-muted-foreground">Total Profit: </span>
              <span className={cn(
                "font-medium",
                data.totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
              )}>
                ${data.totalProfit.toFixed(2)}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Win Rate: </span>
              <span className="font-medium">{data.winRate.toFixed(1)}%</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Trades: </span>
              <span className="font-medium">{data.tradeCount}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between">
        <div className="flex flex-wrap gap-2">
          <Tabs value={selectedMetric} onValueChange={(value) => setSelectedMetric(value as MetricType)}>
            <TabsList className="grid grid-cols-5 w-fit">
              <TabsTrigger value="profit" className="flex items-center gap-1">
                <DollarSign className="h-3 w-3" />
                Profit
              </TabsTrigger>
              <TabsTrigger value="winRate" className="flex items-center gap-1">
                <Target className="h-3 w-3" />
                Win Rate
              </TabsTrigger>
              <TabsTrigger value="tradeCount" className="flex items-center gap-1">
                <Activity className="h-3 w-3" />
                Trades
              </TabsTrigger>
              <TabsTrigger value="averageProfit" className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                Avg
              </TabsTrigger>
              <TabsTrigger value="profitFactor" className="flex items-center gap-1">
                <Scale className="h-3 w-3" />
                Factor
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="flex flex-wrap gap-2">
          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as TimeRange)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as "asc" | "desc")}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="desc">Highest First</SelectItem>
              <SelectItem value="asc">Lowest First</SelectItem>
            </SelectContent>
          </Select>

          <Tabs value={chartType} onValueChange={(value) => setChartType(value as ChartType)}>
            <TabsList className="grid grid-cols-2 w-fit">
              <TabsTrigger value="bar" className="flex items-center gap-1">
                <BarChart3 className="h-3 w-3" />
                Bar
              </TabsTrigger>
              <TabsTrigger value="pie" className="flex items-center gap-1">
                <PieChartIcon className="h-3 w-3" />
                Pie
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Chart Section */}
      {symbolData.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="h-12 w-12 rounded-lg bg-muted flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-medium">No Data Available</h3>
              <p className="text-sm text-muted-foreground">
                No trading data found for the selected time range
              </p>
            </div>
          </div>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {chartType === "bar" ? <BarChart3 className="h-5 w-5" /> : <PieChartIcon className="h-5 w-5" />}
              {getMetricLabel(selectedMetric)} by Symbol
            </CardTitle>
            <CardDescription>
              {chartType === "bar" ? "Bar chart" : "Pie chart"} showing {getMetricLabel(selectedMetric).toLowerCase()} across different symbols
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[400px]">
              {chartType === "bar" ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis
                      dataKey="symbol"
                      angle={-45}
                      textAnchor="end"
                      height={60}
                      className="text-xs"
                    />
                    <YAxis
                      tickFormatter={(value) => formatMetricValue(value, selectedMetric)}
                      className="text-xs"
                    />
                    <Tooltip content={<CustomTooltip />} />
                    {(selectedMetric === "profit" || selectedMetric === "averageProfit") && (
                      <ReferenceLine y={0} stroke="hsl(var(--muted-foreground))" strokeDasharray="2 2" />
                    )}
                    {selectedMetric === "profitFactor" && (
                      <ReferenceLine y={1} stroke="hsl(var(--muted-foreground))" strokeDasharray="2 2" />
                    )}
                    <Bar
                      dataKey={selectedMetric === "profit" ? "totalProfit" : selectedMetric}
                      radius={[4, 4, 0, 0]}
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={80}
                      outerRadius={120}
                      dataKey={selectedMetric === "profit" ? "totalProfit" : selectedMetric}
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [formatMetricValue(Number(value), selectedMetric), getMetricLabel(selectedMetric)]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Cards */}
      {symbolData.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {symbolData.slice(0, 6).map((data) => (
            <Card key={data.symbol} className="relative overflow-hidden">
              <div className={cn(
                "absolute top-0 left-0 w-1 h-full",
                data.totalProfit >= 0 ? "bg-emerald-500" : "bg-rose-500"
              )} />
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{data.symbol}</CardTitle>
                  <Badge variant={data.totalProfit >= 0 ? "default" : "destructive"}>
                    ${data.totalProfit.toFixed(2)}
                  </Badge>
                </div>
                <CardDescription>
                  {data.tradeCount} trades • {data.wins}W / {data.losses}L
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Win Rate</span>
                      <span className="font-medium">{data.winRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Avg Profit</span>
                      <span className={cn(
                        "font-medium",
                        data.averageProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${data.averageProfit.toFixed(2)}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Profit Factor</span>
                      <span className="font-medium">
                        {data.profitFactor === Infinity ? "∞" : data.profitFactor.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Volume</span>
                      <span className="font-medium">{data.totalVolume.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
