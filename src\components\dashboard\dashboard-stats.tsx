"use client"

import { StatsCard } from "@/components/dashboard/stats-card"
import { type ProcessedData } from "@/lib/excel-processor"
import type { Trade } from "@/types/trade"

// Dashboard-specific data type that uses database trades
interface DashboardData {
  account: {
    name: string
    account: string
    company: string
    date: string
  }
  trades: Trade[]
  summary: ProcessedData["summary"]
}

interface DashboardStatsProps {
  tradeData: DashboardData | null
  calculateRiskReward: (trades: Trade[]) => number
}

export function DashboardStats({ tradeData, calculateRiskReward }: DashboardStatsProps) {
  // Calculate metrics directly from trades for more accurate and up-to-date stats
  const trades = tradeData?.trades || [];
  const totalTrades = trades.length;
  const winningTrades = trades.filter(t => t.profit > 0);
  const losingTrades = trades.filter(t => t.profit < 0);

  const winCount = winningTrades.length;
  const lossCount = losingTrades.length;

  // Calculate win rate percentage directly from trades
  const winRatePercentage = totalTrades > 0 ? (winCount / totalTrades) * 100 : 0;
  const winRateFormatted = winRatePercentage.toFixed(2) + '%';

  // Calculate total profit/loss directly from trades
  const totalProfit = trades.reduce((sum, trade) => sum + trade.profit, 0);

  // Use initial balance from summary if available
  const initialBalance = tradeData?.summary?.initial_balance || 10000;

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
      {/* Balance Card */}
      <StatsCard
        title="Balance"
        value={(initialBalance + totalProfit).toFixed(2)}
        prefix="$"
        valueClassName="text-green-500"
      />

      {/* Total Trades Card */}
      <StatsCard
        title="Total Trades"
        value={totalTrades.toString()}
        gaugeValue={Math.min((totalTrades / 200) * 100, 100)}
        gaugeMax={100}
        gaugeColor="stroke-blue-500"
        leftLabel="0"
        rightLabel="200+"
      />

      {/* Win Rate Card */}
      <StatsCard
        title="Win Rate"
        value={winRateFormatted}
        gaugeValue={winRatePercentage}
        gaugeMax={100}
        gaugeColor="stroke-green-500"
        leftLabel={`${winCount}`}
        rightLabel={`${lossCount}`}
        useDualColorGauge={true}
        leftValue={winCount}
        rightValue={lossCount}
        leftColor="stroke-emerald-500"
        rightColor="stroke-rose-500"
      />

      {/* Total P&L Card */}
      <StatsCard
        title="Total P&L"
        value={totalProfit.toFixed(2)}
        prefix="$"
        valueClassName={totalProfit >= 0 ? "text-green-500" : "text-red-500"}
      />

      {/* Avg Win/Avg Loss Card */}
      <StatsCard
        title="Avg Win/Avg Loss"
        value={calculateRiskReward(trades).toFixed(2)}
        gaugeValue={Math.min(calculateRiskReward(trades) * 20, 100)}
        gaugeMax={100}
        gaugeColor="stroke-amber-500"
        leftLabel={`$${winningTrades.length > 0 ?
          (winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length).toFixed(0) : "0"}`}
        rightLabel={`$${losingTrades.length > 0 ?
          (Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length)).toFixed(0) : "0"}`}
        useDualColorGauge={true}
        leftValue={winningTrades.length > 0 ?
          winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length : 1}
        rightValue={losingTrades.length > 0 ?
          Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length) : 1}
        leftColor="stroke-emerald-500"
        rightColor="stroke-rose-500"
      />
    </div>
  )
}
