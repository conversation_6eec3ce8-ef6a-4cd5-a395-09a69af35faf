"use client"

import { cn } from "@/lib/utils"

interface SemiCircularProgressProps {
  value: number
  max: number
  size?: number
  strokeWidth?: number
  className?: string
  progressColor?: string
  backgroundColor?: string
  showLabels?: boolean
  leftLabel?: string
  rightLabel?: string
  showPercentage?: boolean
  percentageClassName?: string
}

export function SemiCircularProgress({
  value,
  max,
  size = 60,
  strokeWidth = 6,
  className,
  progressColor = "stroke-emerald-500",
  backgroundColor = "stroke-muted",
  showLabels = false,
  leftLabel = "",
  rightLabel = "",
  showPercentage = true,
  percentageClassName = ""
}: SemiCircularProgressProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * Math.PI
  // Ensure value and max are valid numbers
  const safeValue = isNaN(value) ? 0 : value
  const safeMax = isNaN(max) || max <= 0 ? 100 : max
  const progress = Math.min(Math.max(safeValue, 0), safeMax) / safeMax
  // For left-to-right fill, we need to reverse the offset calculation
  const progressOffset = circumference * (1 - progress)
  // Ensure progressOffset is a valid number
  const safeProgressOffset = isNaN(progressOffset) ? 0 : progressOffset

  return (
    <div className={cn("relative flex flex-col items-center", className)}>
      <svg
        width={size}
        height={size / 2 + strokeWidth}
        viewBox={`0 0 ${size} ${size / 2 + strokeWidth}`}
      >
        {/* Background semi-circle */}
        <path
          d={`M ${strokeWidth / 2} ${size / 2}
              a ${radius} ${radius} 0 0 1 ${size - strokeWidth} 0`}
          fill="none"
          strokeWidth={strokeWidth}
          className={backgroundColor}
          strokeLinecap="round"
        />

        {/* Progress semi-circle */}
        <path
          d={`M ${strokeWidth / 2} ${size / 2}
              a ${radius} ${radius} 0 0 1 ${size - strokeWidth} 0`}
          fill="none"
          strokeWidth={strokeWidth}
          className={progressColor}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={safeProgressOffset.toString()}
        />
      </svg>

      {/* Value display */}
      {showPercentage && (
        <div className={cn("absolute top-[calc(50%-10px)] text-sm font-medium", percentageClassName)}>
          {Math.round((progress || 0) * 100)}%
        </div>
      )}

      {/* Labels */}
      {showLabels && (
        <div className="absolute w-full flex justify-between bottom-[-12px] text-[9px] text-muted-foreground">
          <span className="ml-0">{leftLabel}</span>
          <span className="mr-0">{rightLabel}</span>
        </div>
      )}
    </div>
  )
}
