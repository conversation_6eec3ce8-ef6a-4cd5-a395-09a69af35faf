import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { filterTrades } from './filter-utils';
import { DashboardFilters } from '@/components/dashboard/dashboard-filters';
import { isWeekend } from 'date-fns';

/**
 * Create a server-side Supabase client
 */
export async function getServerSupabase() {
  const cookieStore = await cookies();
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );
}

/**
 * Get the authenticated user from Supabase
 */
export async function getAuthenticatedUser() {
  const supabase = await getServerSupabase();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    return { user: null, error };
  }
  
  return { user, error: null };
}

/**
 * Get user accounts from Supabase
 */
export async function getUserAccounts(userId: string) {
  const supabase = await getServerSupabase();
  
  const { data: accounts, error } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });
    
  return { accounts, error };
}

/**
 * Get the selected account ID from user preferences
 */
export async function getSelectedAccountId(userId: string) {
  const { accounts, error } = await getUserAccounts(userId);
  
  if (error || !accounts || accounts.length === 0) {
    return null;
  }
  
  return accounts[0].id;
}

/**
 * Get trading summary for an account
 */
export async function getTradingSummary(accountId: string) {
  const supabase = await getServerSupabase();
  
  const { data: summary, error } = await supabase
    .from("trading_summaries")
    .select("*")
    .eq("account_id", accountId)
    .maybeSingle();
    
  return { summary, error };
}

/**
 * Get trades for an account
 */
export async function getAccountTrades(accountId: string) {
  const supabase = await getServerSupabase();
  
  const { data: trades, error } = await supabase
    .from("trades")
    .select("*")
    .eq("account_id", accountId)
    .order("time_close", { ascending: false });
    
  return { trades: trades || [], error };
}

/**
 * Get custom metrics for a user
 */
export async function getCustomMetrics(userId: string) {
  const supabase = await getServerSupabase();
  
  const { data: metrics, error } = await supabase
    .from('custom_metrics')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
    
  return { metrics: metrics || [], error };
}

/**
 * Get goals for a user
 */
export async function getGoals(userId: string) {
  const supabase = await getServerSupabase();
  
  const { data: goals, error } = await supabase
    .from('goals')
    .select('*, custom_metrics(*)')
    .eq('user_id', userId)
    .order('end_date', { ascending: true });
    
  return { goals: goals || [], error };
}

/**
 * Get all unique symbols from trades
 */
export function getUniqueSymbols(trades: any[]) {
  const symbols = new Set<string>();
  
  trades.forEach(trade => {
    if (trade.symbol) {
      symbols.add(trade.symbol);
    }
  });
  
  return Array.from(symbols).sort();
}

/**
 * Filter trades on the server side
 */
export function serverFilterTrades(trades: any[], filters: DashboardFilters) {
  return filterTrades(trades, filters);
}

/**
 * Get all dashboard data for a user
 */
export async function getDashboardData(userId: string) {
  // Get selected account ID
  const accountId = await getSelectedAccountId(userId);
  
  if (!accountId) {
    return {
      summary: null,
      trades: [],
      metrics: [],
      goals: [],
      uniqueSymbols: [],
    };
  }
  
  // Get trading summary
  const { summary } = await getTradingSummary(accountId);
  
  // Get trades
  const { trades } = await getAccountTrades(accountId);
  
  // Get custom metrics
  const { metrics } = await getCustomMetrics(userId);
  
  // Get goals
  const { goals } = await getGoals(userId);
  
  // Get unique symbols
  const uniqueSymbols = getUniqueSymbols(trades);
  
  return {
    summary,
    trades,
    metrics,
    goals,
    uniqueSymbols,
  };
}
