"use client"

import React, { useState, useEffect } from "react"
import { format, formatDistanceStrict, startOfMonth, endOfMonth, eachDayOfInterval, startOfWeek, endOfWeek, addDays, subDays, isWeekend } from "date-fns"
import { getDay } from "date-fns"
import { ChevronLeft as ChevronLeftIcon, ChevronRight as ChevronRightIcon, Clock as ClockIcon, FileText as FileTextIcon, AlertCircle as AlertCircleIcon, ArrowUp as ArrowUpIcon, Home as HomeIcon, BarChart as BarChartIcon } from "lucide-react"
import { Strategy } from "@/types/playbook"
import { getStrategyById, getTradesByStrategy } from "@/lib/playbook-service"
import { hasJournalEntry, getJournalEntryForDate } from "@/lib/journal-utils"
import { getSupabaseBrowser } from "@/lib/supabase"
import { useCalendarData, getDayTrades, getDayMetrics, hasNotes, getNote, CalendarData, DayMetrics } from "@/hooks/use-calendar-data"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CalendarViewSelector } from "@/components/calendar-view-selector"
import { TradingDayNote } from "@/components/trading-day-note"
import { StrategySelector } from "@/components/strategy-selector"
import { StrategyDayPerformance } from "@/components/strategy-day-performance"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { type ProcessedData } from "@/lib/excel-processor"
import type { Trade } from "@/types/trade"


interface EconomicEvent {
  date: string;
  time: string;
  currency: string;
  impact: 'high' | 'medium' | 'low';
  event: string;
  actual?: string;
  forecast?: string;
  previous?: string;
}

interface TradingSession {
  name: string;
  startHour: number;
  endHour: number;
  color: string;
}

interface TradingCalendarProps {
  trades: Trade[]
  onSelectDate: (date: Date, trades: Trade[]) => void
  userId?: string
  accountId?: string | null // Add accountId prop to detect account changes
}

// Define trading sessions
const TRADING_SESSIONS: TradingSession[] = [
  { name: 'Sydney', startHour: 22, endHour: 7, color: 'bg-purple-500/20' },
  { name: 'Tokyo', startHour: 0, endHour: 9, color: 'bg-blue-500/20' },
  { name: 'London', startHour: 8, endHour: 17, color: 'bg-green-500/20' },
  { name: 'New York', startHour: 13, endHour: 22, color: 'bg-amber-500/20' },
];

// Sample economic events (in a real app, these would come from an API)
const SAMPLE_ECONOMIC_EVENTS: EconomicEvent[] = [
  {
    date: '2023-06-14',
    time: '08:30',
    currency: 'USD',
    impact: 'high',
    event: 'CPI m/m',
    actual: '0.1%',
    forecast: '0.2%',
    previous: '0.4%'
  },
  {
    date: '2023-06-14',
    time: '14:00',
    currency: 'USD',
    impact: 'high',
    event: 'FOMC Statement',
    actual: '',
    forecast: '',
    previous: ''
  },
  {
    date: '2023-06-15',
    time: '12:30',
    currency: 'EUR',
    impact: 'medium',
    event: 'ECB Press Conference',
    actual: '',
    forecast: '',
    previous: ''
  },
];

export function TradingCalendar({ trades, onSelectDate, userId, accountId }: TradingCalendarProps) {
  // Find the date of the first trade to set as default month
  const getFirstTradeMonth = () => {
    if (!trades || trades.length === 0) {
      console.log('No trades found, using current month')
      return new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    }

    // Sort trades by date (oldest first)
    const sortedTrades = [...trades].sort(
      (a, b) => new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    )

    // Get the date of the first trade
    const firstTradeDate = new Date(sortedTrades[0].time_close)
    console.log('First trade date:', format(firstTradeDate, 'yyyy-MM-dd'))

    // Return the first day of that month
    return new Date(firstTradeDate.getFullYear(), firstTradeDate.getMonth(), 1)
  }

  // Initialize with current date, will be updated in useEffect
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())
  const [calendarView, setCalendarView] = useState<"month" | "week" | "day">("month")
  const [tradingNotes, setTradingNotes] = useState<Record<string, string>>({})
  const [showTradingSessions, setShowTradingSessions] = useState<boolean>(false)
  const [showEconomicEvents, setShowEconomicEvents] = useState<boolean>(false)
  const [showStrategyOverlay, setShowStrategyOverlay] = useState<boolean>(false)
  const [selectedStrategyId, setSelectedStrategyId] = useState<string | null>(null)
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null)
  const [strategyTrades, setStrategyTrades] = useState<Record<string, any[]>>({}) // Cache for strategy trades by date
  const [economicEvents] = useState<EconomicEvent[]>(SAMPLE_ECONOMIC_EVENTS)
  const [breadcrumbs, setBreadcrumbs] = useState<{view: "month" | "week" | "day", date: Date}[]>([])
  const [heatmapIntensity, setHeatmapIntensity] = useState<"low" | "medium" | "high">("medium")

  // Fetch calendar data from the server
  const {
    data: calendarData,
    isLoading: isCalendarLoading,
    error: calendarError
  } = useCalendarData(
    currentMonth.getFullYear(),
    currentMonth.getMonth(),
    accountId || null, // Ensure accountId is never undefined
    calendarView
  ) as { data: CalendarData | null, isLoading: boolean, error: Error | null }

  // Log any errors for debugging
  useEffect(() => {
    if (calendarError) {
      console.error('Calendar data error:', calendarError)
    }
  }, [calendarError])

  // No longer using advanced filters at the calendar level

  // Set the initial month only once when the component first mounts
  const [, setHasInitialized] = useState(false)

  // Reset state when accountId or trades change
  useEffect(() => {
    // Reset initialization flag to recalculate the first trade month
    setHasInitialized(false)
    // Reset breadcrumbs to go back to month view
    setBreadcrumbs([])
    // Reset calendar view to month
    setCalendarView("month")
    // Reset current month to the first trade month
    const firstTradeMonth = getFirstTradeMonth()
    console.log('Setting calendar to first trade month:', format(firstTradeMonth, 'MMMM yyyy'))
    setCurrentMonth(firstTradeMonth)
  }, [accountId, trades]) // eslint-disable-line react-hooks/exhaustive-deps

  // Update trading notes from server data
  useEffect(() => {
    if (calendarData && calendarData.tradingNotes) {
      setTradingNotes(calendarData.tradingNotes);
    }
  }, [calendarData])

  // Update dates with journal entries from server data
  useEffect(() => {
    if (calendarData && calendarData.datesWithJournalEntries) {
      setDatesWithJournalEntries(new Set(calendarData.datesWithJournalEntries));
    }
  }, [calendarData])

  // Listen for journal entry found events to update the UI
  useEffect(() => {
    const handleJournalEntryFound = (event: Event) => {
      // Get the date from the event detail
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.date) {
        const dateKey = customEvent.detail.date;

        // Update our set of dates with journal entries
        setDatesWithJournalEntries(prev => {
          const newSet = new Set(prev);
          newSet.add(dateKey);
          return newSet;
        });
      }
    }

    document.addEventListener('journal-entry-found', handleJournalEntryFound)

    return () => {
      document.removeEventListener('journal-entry-found', handleJournalEntryFound)
    }
  }, [])

  // Save notes to Supabase whenever they change
  useEffect(() => {
    const saveNotesToSupabase = async () => {
      if (!userId || Object.keys(tradingNotes).length === 0) return;

      try {
        const supabase = getSupabaseBrowser();

        // For each note, upsert it to Supabase
        for (const [dateKey, content] of Object.entries(tradingNotes)) {
          if (!content.trim()) continue; // Skip empty notes

          // Check if note already exists
          let query = supabase
            .from('trading_notes')
            .select('id')
            .eq('user_id', userId)
            .eq('date', dateKey);

          // Add account filter if provided
          if (accountId) {
            query = query.eq('account_id', accountId);
          } else {
            query = query.is('account_id', null);
          }

          const { data: existingNote, error: checkError } = await query.maybeSingle();

          if (checkError) {
            console.error(`Error checking for existing note for ${dateKey}:`, checkError);
            continue;
          }

          if (existingNote) {
            // Update existing note
            const { error: updateError } = await supabase
              .from('trading_notes')
              .update({ content, updated_at: new Date().toISOString() })
              .eq('id', existingNote.id);

            if (updateError) {
              console.error(`Error updating note for ${dateKey}:`, updateError);
            }
          } else {
            // Insert new note
            const { error: insertError } = await supabase
              .from('trading_notes')
              .insert({
                user_id: userId,
                account_id: accountId || null,
                date: dateKey,
                content,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });

            if (insertError) {
              console.error(`Error inserting note for ${dateKey}:`, insertError);
            }
          }
        }
      } catch (error) {
        console.error('Error saving trading notes:', error);
      }
    };

    // Debounce the save operation to avoid too many database calls
    const timeoutId = setTimeout(saveNotesToSupabase, 1000);
    return () => clearTimeout(timeoutId);
  }, [tradingNotes, userId, accountId])

  // This effect is no longer needed as we handle initialization in the accountId/trades effect above
  // useEffect(() => {
  //   if (!hasInitialized && trades && trades.length > 0) {
  //     setCurrentMonth(getFirstTradeMonth())
  //     setHasInitialized(true)
  //   }
  // }, [trades, hasInitialized]) // eslint-disable-line react-hooks/exhaustive-deps

  // Fetch strategy data when a strategy is selected
  useEffect(() => {
    const fetchStrategyData = async () => {
      if (!userId || !selectedStrategyId) {
        setSelectedStrategy(null)
        setStrategyTrades({})
        return
      }

      try {
        // Fetch strategy details
        const strategy = await getStrategyById(userId, selectedStrategyId)
        setSelectedStrategy(strategy)

        if (strategy) {
          // Fetch strategy trades with account filtering
          const trades = await getTradesByStrategy(userId, selectedStrategyId, accountId)

          // Group trades by date for easier lookup
          const tradesByDate: Record<string, any[]> = {}
          trades.forEach(trade => {
            const dateKey = format(new Date(trade.time_close), "yyyy-MM-dd")
            if (!tradesByDate[dateKey]) {
              tradesByDate[dateKey] = []
            }
            tradesByDate[dateKey].push(trade)
          })

          setStrategyTrades(tradesByDate)
        }
      } catch (error) {
        console.error("Error fetching strategy data:", error)
      }
    }

    fetchStrategyData()
  }, [userId, selectedStrategyId, accountId])

  const nextPeriod = () => {
    switch (calendarView) {
      case "month":
        setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))
        break
      case "week":
        setCurrentMonth(addDays(currentMonth, 7))
        break
      case "day":
        setCurrentMonth(addDays(currentMonth, 1))
        break
    }
  }

  const prevPeriod = () => {
    switch (calendarView) {
      case "month":
        setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))
        break
      case "week":
        setCurrentMonth(subDays(currentMonth, 7))
        break
      case "day":
        setCurrentMonth(subDays(currentMonth, 1))
        break
    }
  }

  // Handle drill down navigation
  const drillDown = (date: Date) => {
    // Save current view to breadcrumbs
    setBreadcrumbs([...breadcrumbs, { view: calendarView, date: currentMonth }])

    // Set the new date
    setCurrentMonth(date)

    // Change the view based on current view
    if (calendarView === "month") {
      setCalendarView("week")
    } else if (calendarView === "week") {
      setCalendarView("day")
    }
  }

  // Navigate back up using breadcrumbs
  const navigateUp = () => {
    if (breadcrumbs.length === 0) return

    // Get the last breadcrumb
    const lastBreadcrumb = breadcrumbs[breadcrumbs.length - 1]

    // Set the view and date from the breadcrumb
    setCalendarView(lastBreadcrumb.view)
    setCurrentMonth(lastBreadcrumb.date)

    // Remove the last breadcrumb
    setBreadcrumbs(breadcrumbs.slice(0, -1))
  }

  // State to track dates with journal entries
  const [datesWithJournalEntries, setDatesWithJournalEntries] = useState<Set<string>>(new Set())

  // Check if a date has notes
  const hasNotes = (date: Date): boolean => {
    // Check both trading notes and journal entries
    const dateKey = format(date, "yyyy-MM-dd")
    const hasTradingNote = !!tradingNotes[dateKey] && tradingNotes[dateKey].trim() !== ""
    const hasJournal = datesWithJournalEntries.has(dateKey)

    return hasTradingNote || hasJournal
  }

  // Force the loading indicator to stop when the component mounts or when data changes
  useEffect(() => {
    // Force the loading indicator to stop after a timeout
    // This ensures the loading bar doesn't get stuck waiting for journal entries
    const loadingTimer = setTimeout(() => {
      // Force the loading indicator to complete
      window.history.pushState(null, '', window.location.href)

      // Try to access NProgress directly
      if (typeof window !== 'undefined') {
        const anyWindow = window as any
        if (anyWindow.NProgress) {
          anyWindow.NProgress.done()
        }

        // Try to find and manipulate the loading bar element directly
        const loadingBar = document.getElementById('nprogress')
        if (loadingBar) {
          loadingBar.style.display = 'none'
        }
      }
    }, 1000)

    // Clean up function
    return () => {
      clearTimeout(loadingTimer)

      // Force the loading indicator to stop on unmount
      window.history.pushState(null, '', window.location.href)

      // Try to access NProgress directly
      if (typeof window !== 'undefined') {
        const anyWindow = window as any
        if (anyWindow.NProgress) {
          anyWindow.NProgress.done()
        }

        // Try to find and manipulate the loading bar element directly
        const loadingBar = document.getElementById('nprogress')
        if (loadingBar) {
          loadingBar.style.display = 'none'
        }
      }
    }
  }, [calendarData, isCalendarLoading])

  // Check if a date has economic events
  const hasEconomicEvents = (date: Date): boolean => {
    if (!showEconomicEvents) return false
    const dateStr = format(date, "yyyy-MM-dd")
    return economicEvents.some(event => event.date === dateStr)
  }

  // Get economic events for a date
  const getEconomicEvents = (date: Date): EconomicEvent[] => {
    const dateStr = format(date, "yyyy-MM-dd")
    return economicEvents.filter(event => event.date === dateStr)
  }

  const getDaysToDisplay = () => {
    // If we have calendar data from the server, use it
    if (calendarData && calendarData.daysToDisplay) {
      return calendarData.daysToDisplay.map((dateStr: string) => new Date(dateStr));
    }

    // Otherwise, fall back to client-side calculation
    switch (calendarView) {
      case "month":
        // Get the first day of the month
        const monthStart = startOfMonth(currentMonth)
        const monthEnd = endOfMonth(currentMonth)

        // Get all days in the month
        const daysInMonth = eachDayOfInterval({
          start: monthStart,
          end: monthEnd,
        })

        // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
        const firstDayOfWeek = getDay(monthStart)

        // Add days from the previous month to align with the correct day of week
        const prevMonthDays = Array.from({ length: firstDayOfWeek }, (_, i) => {
          // Create dates for the previous month days
          return new Date(monthStart.getFullYear(), monthStart.getMonth(), -firstDayOfWeek + i + 1)
        })

        // Calculate how many days we need from the next month to complete the grid
        // We want to ensure we have complete weeks (multiples of 7)
        const totalDaysSoFar = prevMonthDays.length + daysInMonth.length
        const remainingDays = (Math.ceil(totalDaysSoFar / 7) * 7) - totalDaysSoFar

        // Add days from the next month
        const nextMonthDays = Array.from({ length: remainingDays }, (_, i) => {
          // Create dates for the next month days
          return new Date(monthEnd.getFullYear(), monthEnd.getMonth(), monthEnd.getDate() + i + 1)
        })

        return [...prevMonthDays, ...daysInMonth, ...nextMonthDays]
      case "week":
        return eachDayOfInterval({
          start: startOfWeek(currentMonth),
          end: endOfWeek(currentMonth),
        })
      case "day":
        return [currentMonth]
    }
  }

  const daysToDisplay = getDaysToDisplay()

  const saveNote = (date: Date, note: string) => {
    const dateKey = format(date, "yyyy-MM-dd")
    setTradingNotes(prev => ({
      ...prev,
      [dateKey]: note
    }))
  }

  const getNote = (date: Date): string => {
    const dateKey = format(date, "yyyy-MM-dd")
    return tradingNotes[dateKey] || ""
  }

  // No longer filtering trades at the calendar level - filtering is done at the dashboard level

  const getDayTrades = (date: Date) => {
    // If we have calendar data from the server, use it
    if (calendarData && calendarData.tradesByDate) {
      const dateKey = format(date, "yyyy-MM-dd")
      return calendarData.tradesByDate[dateKey] || []
    }

    // Otherwise, fall back to client-side calculation
    if (!trades) return []
    return trades.filter(
      (trade) => format(new Date(trade.time_close), "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    )
  }

  const getDayMetrics = (date: Date) => {
    // If we have calendar data from the server, use it
    if (calendarData && calendarData.dayMetrics) {
      const dateKey = format(date, "yyyy-MM-dd")
      return calendarData.dayMetrics[dateKey] || null
    }

    // Otherwise, fall back to client-side calculation
    const dayTrades = getDayTrades(date)
    if (dayTrades.length === 0) return null

    const totalProfit = dayTrades.reduce((sum: number, trade: any) => sum + trade.profit, 0)
    const winningTrades = dayTrades.filter((trade: any) => trade.profit > 0)
    const losingTrades = dayTrades.filter((trade: any) => trade.profit < 0)
    const winRate = dayTrades.length > 0 ? (winningTrades.length / dayTrades.length) * 100 : 0

    return {
      totalProfit,
      tradeCount: dayTrades.length,
      winCount: winningTrades.length,
      lossCount: losingTrades.length,
      winRate: winRate.toFixed(0) + '%',
      isProfitable: totalProfit > 0
    }
  }

  // Get strategy metrics for a specific day
  const getStrategyDayMetrics = (date: Date) => {
    if (!selectedStrategyId || !showStrategyOverlay) return null

    const dateKey = format(date, "yyyy-MM-dd")
    const dayTrades = strategyTrades[dateKey] || []

    if (dayTrades.length === 0) return null

    const totalProfit = dayTrades.reduce((sum: number, trade: any) => sum + trade.profit, 0)
    const winningTrades = dayTrades.filter((trade: any) => trade.profit > 0)
    const losingTrades = dayTrades.filter((trade: any) => trade.profit < 0)
    const winRate = dayTrades.length > 0 ? (winningTrades.length / dayTrades.length) * 100 : 0

    return {
      totalProfit,
      tradeCount: dayTrades.length,
      winCount: winningTrades.length,
      lossCount: losingTrades.length,
      winRate: winRate.toFixed(0) + '%',
      isProfitable: totalProfit > 0
    }
  }

  // Get weekly metrics for a row of days
  const getWeeklyMetrics = (weekDays: Date[], weekIndex: number) => {
    // If we have calendar data from the server, use it
    if (calendarData && calendarData.weeklyMetrics && calendarData.weeklyMetrics[weekIndex]) {
      return calendarData.weeklyMetrics[weekIndex]
    }

    // Otherwise, fall back to client-side calculation
    // Filter only days from current month and get their trades
    const weekTrades = weekDays
      .filter(day => day.getMonth() === currentMonth.getMonth())
      .flatMap(day => getDayTrades(day))

    if (weekTrades.length === 0) return null

    const totalProfit = weekTrades.reduce((sum: number, trade: any) => sum + trade.profit, 0)
    const winningTrades = weekTrades.filter((trade: any) => trade.profit > 0)
    const losingTrades = weekTrades.filter((trade: any) => trade.profit < 0)
    const winRate = weekTrades.length > 0 ? (winningTrades.length / weekTrades.length) * 100 : 0

    return {
      totalProfit,
      tradeCount: weekTrades.length,
      winCount: winningTrades.length,
      lossCount: losingTrades.length,
      winRate: winRate.toFixed(0) + '%',
      isProfitable: totalProfit > 0
    }
  }

  // Get intensity class based on profit/loss and intensity level
  const getIntensityClass = (profit: number, isProfitable: boolean) => {
    // Define classes for different intensity levels
    if (isProfitable) {
      if (profit > 1000) {
        return heatmapIntensity === "low" ? "bg-emerald-500/30 hover:bg-emerald-500/40" :
               heatmapIntensity === "medium" ? "bg-emerald-500/40 hover:bg-emerald-500/50" :
               "bg-emerald-500/50 hover:bg-emerald-500/60"
      } else if (profit > 500) {
        return heatmapIntensity === "low" ? "bg-emerald-500/20 hover:bg-emerald-500/30" :
               heatmapIntensity === "medium" ? "bg-emerald-500/30 hover:bg-emerald-500/40" :
               "bg-emerald-500/40 hover:bg-emerald-500/50"
      } else if (profit > 250) {
        return heatmapIntensity === "low" ? "bg-emerald-500/15 hover:bg-emerald-500/25" :
               heatmapIntensity === "medium" ? "bg-emerald-500/25 hover:bg-emerald-500/35" :
               "bg-emerald-500/35 hover:bg-emerald-500/45"
      } else if (profit > 100) {
        return heatmapIntensity === "low" ? "bg-emerald-500/10 hover:bg-emerald-500/20" :
               heatmapIntensity === "medium" ? "bg-emerald-500/20 hover:bg-emerald-500/30" :
               "bg-emerald-500/30 hover:bg-emerald-500/40"
      } else {
        return heatmapIntensity === "low" ? "bg-emerald-500/5 hover:bg-emerald-500/15" :
               heatmapIntensity === "medium" ? "bg-emerald-500/10 hover:bg-emerald-500/20" :
               "bg-emerald-500/20 hover:bg-emerald-500/30"
      }
    } else {
      if (profit < -1000) {
        return heatmapIntensity === "low" ? "bg-rose-500/30 hover:bg-rose-500/40" :
               heatmapIntensity === "medium" ? "bg-rose-500/40 hover:bg-rose-500/50" :
               "bg-rose-500/50 hover:bg-rose-500/60"
      } else if (profit < -500) {
        return heatmapIntensity === "low" ? "bg-rose-500/20 hover:bg-rose-500/30" :
               heatmapIntensity === "medium" ? "bg-rose-500/30 hover:bg-rose-500/40" :
               "bg-rose-500/40 hover:bg-rose-500/50"
      } else if (profit < -250) {
        return heatmapIntensity === "low" ? "bg-rose-500/15 hover:bg-rose-500/25" :
               heatmapIntensity === "medium" ? "bg-rose-500/25 hover:bg-rose-500/35" :
               "bg-rose-500/35 hover:bg-rose-500/45"
      } else if (profit < -100) {
        return heatmapIntensity === "low" ? "bg-rose-500/10 hover:bg-rose-500/20" :
               heatmapIntensity === "medium" ? "bg-rose-500/20 hover:bg-rose-500/30" :
               "bg-rose-500/30 hover:bg-rose-500/40"
      } else {
        return heatmapIntensity === "low" ? "bg-rose-500/5 hover:bg-rose-500/15" :
               heatmapIntensity === "medium" ? "bg-rose-500/10 hover:bg-rose-500/20" :
               "bg-rose-500/20 hover:bg-rose-500/30"
      }
    }
  }

  // Check if a day has strategy trades
  const hasStrategyTrades = (date: Date) => {
    if (!selectedStrategyId || !showStrategyOverlay) return false

    const dateKey = format(date, "yyyy-MM-dd")
    return !!strategyTrades[dateKey] && strategyTrades[dateKey].length > 0
  }

  return (
    <Card className="dashboard-card bg-card hover:bg-card transition-all duration-300">
      <CardHeader className="flex flex-col space-y-2 pb-2">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0">
          <div className="flex items-center gap-4">
            <CardTitle className="text-base font-medium">Trading Activity Calendar</CardTitle>
            <CalendarViewSelector
              view={calendarView}
              onChange={setCalendarView}
            />
          </div>
          <div className="flex flex-wrap items-center gap-2">
            {breadcrumbs.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={navigateUp}
                className="flex items-center gap-1 hover:bg-accent"
              >
                <ArrowUpIcon className="h-3 w-3" />
                <span className="hidden sm:inline">Back to {breadcrumbs[breadcrumbs.length - 1].view}</span>
                <span className="sm:hidden">Back</span>
              </Button>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={showTradingSessions ? "default" : "ghost"}
                    size="icon"
                    onClick={() => setShowTradingSessions(!showTradingSessions)}
                    className={cn(
                      "h-8 w-8",
                      showTradingSessions && "bg-primary text-primary-foreground hover:bg-primary/90"
                    )}
                  >
                    <ClockIcon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{showTradingSessions ? "Hide" : "Show"} trading sessions</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={showEconomicEvents ? "default" : "ghost"}
                    size="icon"
                    onClick={() => setShowEconomicEvents(!showEconomicEvents)}
                    className={cn(
                      "h-8 w-8",
                      showEconomicEvents && "bg-primary text-primary-foreground hover:bg-primary/90"
                    )}
                  >
                    <AlertCircleIcon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{showEconomicEvents ? "Hide" : "Show"} economic events</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {userId && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showStrategyOverlay ? "default" : "ghost"}
                      size="icon"
                      onClick={() => setShowStrategyOverlay(!showStrategyOverlay)}
                      className={cn(
                        "h-8 w-8",
                        showStrategyOverlay && "bg-primary text-primary-foreground hover:bg-primary/90"
                      )}
                    >
                      <BarChartIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{showStrategyOverlay ? "Hide" : "Show"} strategy performance</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            <Button
              variant="ghost"
              size="icon"
              onClick={prevPeriod}
              className="hover:bg-accent h-8 w-8"
            >
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium">
              {calendarView === "day"
                ? format(currentMonth, "MMM d, yyyy")
                : calendarView === "week"
                  ? <>
                      <span className="hidden sm:inline">{`${format(startOfWeek(currentMonth), "MMM d")} - ${format(endOfWeek(currentMonth), "MMM d, yyyy")}`}</span>
                      <span className="sm:hidden">{`${format(startOfWeek(currentMonth), "MMM d")}-${format(endOfWeek(currentMonth), "d")}`}</span>
                    </>
                  : <>
                      <span className="hidden sm:inline">{format(currentMonth, "MMMM yyyy")}</span>
                      <span className="sm:hidden">{format(currentMonth, "MMM yyyy")}</span>
                    </>
              }
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={nextPeriod}
              className="hover:bg-accent h-8 w-8"
            >
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Trading sessions legend */}
        {showTradingSessions && (
          <div className="flex flex-wrap items-center gap-2 text-xs">
            <span className="text-muted-foreground">Trading Sessions:</span>
            {TRADING_SESSIONS.map((session) => (
              <Badge key={session.name} variant="outline" className={cn("font-normal", session.color)}>
                <span className="hidden sm:inline">{session.name} ({session.startHour}:00-{session.endHour}:00 UTC)</span>
                <span className="sm:hidden">{session.name}</span>
              </Badge>
            ))}
          </div>
        )}

        {/* Advanced filters removed - now handled at dashboard level */}

        {/* Heatmap intensity selector */}
        <div className="flex items-center gap-2 text-xs">
          <span className="text-muted-foreground">Heatmap Intensity:</span>
          <Select value={heatmapIntensity} onValueChange={(value) => setHeatmapIntensity(value as "low" | "medium" | "high")}>
            <SelectTrigger className="h-7 text-xs w-24">
              <SelectValue placeholder="Intensity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Strategy selector */}
        {showStrategyOverlay && userId && (
          <div className="flex items-center gap-2 text-xs">
            <span className="text-muted-foreground">Strategy Performance:</span>
            <StrategySelector
              userId={userId}
              selectedStrategyId={selectedStrategyId}
              onSelectStrategy={setSelectedStrategyId}
            />
          </div>
        )}

        {/* Breadcrumbs */}
        {breadcrumbs.length > 0 && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs"
              onClick={() => {
                setCalendarView("month")
                setBreadcrumbs([])
              }}
            >
              <HomeIcon className="h-3 w-3 mr-1" />
              Home
            </Button>
            {breadcrumbs.map((crumb, index) => (
              <div key={index} className="flex items-center">
                <span className="mx-1">/</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs"
                  onClick={() => {
                    setCalendarView(crumb.view)
                    setCurrentMonth(crumb.date)
                    setBreadcrumbs(breadcrumbs.slice(0, index))
                  }}
                >
                  {crumb.view} {format(crumb.date,
                    crumb.view === "month" ? "MMM yyyy" :
                    crumb.view === "week" ? "MMM d" :
                    "MMM d, yyyy"
                  )}
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {isCalendarLoading ? (
          <div className="space-y-4">
            <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
            <div className="grid grid-cols-8 gap-1">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-8 bg-muted rounded slow-pulse"></div>
              ))}
              {[...Array(40)].map((_, i) => (
                <div key={`cell-${i}`} className="h-16 bg-muted rounded slow-pulse"></div>
              ))}
            </div>
          </div>
        ) : (
          <>
            {calendarView === "month" && (
          <div className="grid grid-cols-8 gap-1 text-center text-sm">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Weekly Results"].map((day) => (
              <div key={day} className={cn(
                "py-2 font-medium",
                day === "Weekly Results"
                  ? "text-muted-foreground bg-blue-50/50 dark:bg-slate-800/30 rounded-t-sm border-t-2 border-x-2 border-blue-100 dark:border-slate-700/30"
                  : "text-muted-foreground"
              )}>
                <span className="hidden xs:inline">{day}</span>
                <span className="xs:hidden">{day === "Weekly Results" ? "WR" : day.charAt(0)}</span>
              </div>
            ))}
            {/* Group days into weeks and add weekly metrics */}
            {(() => {
              // Group days into weeks (7 days per week)
              const weeks = [];
              for (let i = 0; i < daysToDisplay.length; i += 7) {
                weeks.push(daysToDisplay.slice(i, i + 7));
              }

              return weeks.map((week, weekIndex) => {
                // Calculate weekly metrics
                const weeklyMetrics = getWeeklyMetrics(week, weekIndex);

                return (
                  <React.Fragment key={`week-${weekIndex}`}>
                    {/* Render the 7 days of the week */}
                    {week.map((day: Date) => {
                      // Check if the day is from the current month
                      const isCurrentMonth = day.getMonth() === currentMonth.getMonth()
                      const isWeekendDay = isWeekend(day)

                      // Only get metrics for days in the current month
                      const metrics = isCurrentMonth ? getDayMetrics(day) : null
                      const strategyMetrics = isCurrentMonth ? getStrategyDayMetrics(day) : null
                      const hasActivity = metrics !== null
                      const hasStrategyActivity = strategyMetrics !== null
                      // Check if this day has notes (used in the JSX below)
                      const hasEvents = hasEconomicEvents(day)

                      // Get intensity class based on profit/loss and intensity level
                      let intensityClass = ""
                      if (isCurrentMonth && hasActivity) {
                        intensityClass = getIntensityClass(metrics.totalProfit, metrics.isProfitable)
                      }

                      // Show trading sessions if enabled
                      const showSessions = showTradingSessions && isCurrentMonth && !isWeekendDay

                      return (
                        <div
                          key={day.toString()}
                          className={cn(
                            "relative h-16 xs:h-20 border border-border/50 rounded-sm overflow-hidden",
                            !isCurrentMonth && "opacity-50",
                            isWeekendDay && isCurrentMonth && "bg-muted/30"
                          )}
                        >
                          {/* Trading session overlays */}
                          {showSessions && TRADING_SESSIONS.map((session) => (
                            <div
                              key={`${day}-${session.name}`}
                              className={cn(
                                "absolute top-0 h-1 w-full",
                                session.color
                              )}
                              style={{
                                opacity: 0.7,
                                top: `${(session.startHour / 24) * 100}%`,
                                height: `${((session.endHour - session.startHour) / 24) * 100}%`
                              }}
                            />
                          ))}

                          {/* Strategy performance indicator */}
                          {showStrategyOverlay && hasStrategyActivity && (
                            <div
                              className={cn(
                                "absolute right-0 w-1 h-full",
                                strategyMetrics?.isProfitable ? "bg-emerald-500" : "bg-rose-500"
                              )}
                              style={{
                                opacity: 0.7,
                              }}
                            />
                          )}

                          <Button
                            variant="ghost"
                            className={cn(
                              "h-full w-full hover:bg-accent flex flex-col items-center justify-between p-1 rounded-none",
                              !isCurrentMonth && "text-muted-foreground/50",
                              intensityClass
                            )}
                            onClick={() => {
                              if (isCurrentMonth) {
                                if (hasActivity) {
                                  onSelectDate(day, getDayTrades(day))
                                } else {
                                  drillDown(day)
                                }
                              }
                            }}
                          >
                            <div className="w-full flex justify-between items-start px-1">
                              <time dateTime={format(day, "yyyy-MM-dd")} className={cn(
                                "text-xs font-medium",
                                isWeekendDay && "text-muted-foreground"
                              )}>
                                {format(day, "d")}
                              </time>
                              <div className="flex gap-0.5">
                                {isCurrentMonth && hasActivity && hasNotes(day) && (
                                  <FileTextIcon className="h-2.5 w-2.5 text-foreground dark:text-foreground" />
                                )}
                                {hasEvents && (
                                  <AlertCircleIcon className="h-2.5 w-2.5 text-amber-600 dark:text-amber-500" />
                                )}
                              </div>
                            </div>

                            {isCurrentMonth && hasActivity && (
                              <div className="flex flex-col items-center justify-center leading-tight my-auto py-1">
                                <span className={metrics.isProfitable ? "text-emerald-500 font-medium text-sm" : "text-rose-500 font-medium text-sm"}>
                                  {metrics.isProfitable ? `$${Math.abs(metrics.totalProfit).toFixed(0)}` : `$-${Math.abs(metrics.totalProfit).toFixed(0)}`}
                                </span>
                                <span className="text-muted-foreground text-xs">
                                  {metrics.winCount}/{metrics.lossCount} ({metrics.tradeCount})
                                </span>
                              </div>
                            )}

                            {/* Economic events */}
                            {isCurrentMonth && hasEvents && (
                              <div className="mt-auto w-full px-0.5">
                                {getEconomicEvents(day).slice(0, 1).map((event, i) => (
                                  <div key={i} className="text-[10px] truncate text-amber-400 text-left">
                                    {event.currency}: {event.event}
                                  </div>
                                ))}
                                {getEconomicEvents(day).length > 1 && (
                                  <div className="text-[10px] text-amber-400 text-left">+{getEconomicEvents(day).length - 1} more</div>
                                )}
                              </div>
                            )}
                          </Button>
                        </div>
                      )
                    })}

                    {/* Weekly metrics column */}
                    <div className="relative h-16 xs:h-20 border-2 border-blue-100 dark:border-slate-700/30 rounded-sm overflow-hidden bg-blue-50/30 dark:bg-slate-800/30 shadow-sm">
                      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-slate-700/40 dark:to-slate-600/40"></div>
                      {weeklyMetrics ? (
                        <div className="h-full w-full flex flex-col items-center justify-center p-1">
                          <div className="flex flex-col items-center justify-center leading-tight">
                            <span className={weeklyMetrics.isProfitable ? "text-emerald-500 font-medium text-sm" : "text-rose-500 font-medium text-sm"}>
                              {weeklyMetrics.isProfitable ? `$${Math.abs(weeklyMetrics.totalProfit).toFixed(0)}` : `$-${Math.abs(weeklyMetrics.totalProfit).toFixed(0)}`}
                            </span>
                            <span className="text-muted-foreground text-xs">
                              {weeklyMetrics.winCount}/{weeklyMetrics.lossCount}
                            </span>
                            <span className="text-muted-foreground text-xs">
                              WR: {weeklyMetrics.winRate}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="h-full w-full flex items-center justify-center text-xs text-muted-foreground">
                          No data
                        </div>
                      )}
                    </div>
                  </React.Fragment>
                );
              });
            })()}
          </div>
        )}

        {calendarView === "week" && (
          <div className="grid grid-cols-7 gap-2 text-center">
            {daysToDisplay.map((day: Date) => {
              const metrics = getDayMetrics(day)
              const hasActivity = metrics !== null
              // Check if this day has notes (used in the JSX below)
              const hasEvents = hasEconomicEvents(day)
              const isWeekendDay = isWeekend(day)

              // Get intensity class based on profit/loss and intensity level
              let intensityClass = ""
              if (hasActivity) {
                intensityClass = getIntensityClass(metrics.totalProfit, metrics.isProfitable)
              }

              return (
                <div key={day.toString()} className="flex flex-col">
                  <div className={cn(
                    "py-2 font-medium",
                    isWeekendDay ? "text-muted-foreground" : "text-foreground"
                  )}>
                    {format(day, "EEE")}
                    <div className="font-normal">{format(day, "MMM d")}</div>
                  </div>
                  <div className="relative">
                    {/* Trading session overlays */}
                    {showTradingSessions && !isWeekendDay && TRADING_SESSIONS.map((session) => (
                      <div
                        key={`${day}-${session.name}`}
                        className={cn(
                          "absolute left-0 w-1 h-full",
                          session.color
                        )}
                        style={{
                          opacity: 0.7,
                          left: `${(session.startHour / 24) * 100}%`,
                          width: `${((session.endHour - session.startHour) / 24) * 100}%`
                        }}
                      />
                    ))}

                    <Button
                      variant="ghost"
                      className={cn(
                        "h-32 w-full hover:bg-accent flex flex-col items-center justify-start p-2 relative",
                        isWeekendDay && "bg-muted/30",
                        intensityClass
                      )}
                      onClick={() => {
                        if (hasActivity) {
                          onSelectDate(day, getDayTrades(day))
                        } else {
                          drillDown(day)
                        }
                      }}
                    >
                      <div className="w-full flex justify-end gap-1 mb-2">
                        {hasActivity && hasNotes(day) && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <FileTextIcon className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p>Has trading notes</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {hasEvents && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <AlertCircleIcon className="h-3.5 w-3.5 text-amber-600 dark:text-amber-500" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p>{getEconomicEvents(day).length} economic event(s)</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>

                      {hasActivity ? (
                        <div className="flex flex-col items-center text-xs">
                          <span className={metrics.isProfitable ? "text-emerald-500 font-medium" : "text-rose-500 font-medium"}>
                            {metrics.isProfitable ? `$${Math.abs(metrics.totalProfit).toFixed(2)}` : `$-${Math.abs(metrics.totalProfit).toFixed(2)}`}
                          </span>
                          <span className="text-muted-foreground mt-1">
                            {metrics.winCount} Win / {metrics.lossCount} Loss
                          </span>
                          <span className="text-muted-foreground">
                            Win Rate: {metrics.winRate}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-xs">No trades</span>
                      )}

                      {/* Economic events */}
                      {hasEvents && (
                        <div className="mt-auto w-full px-1 text-left">
                          <div className="text-xs font-medium text-amber-400 mt-2 mb-1">Events:</div>
                          {getEconomicEvents(day).slice(0, 2).map((event, i) => (
                            <div key={i} className="text-[10px] truncate text-amber-400/80 text-left">
                              {event.time} - {event.currency}: {event.event}
                            </div>
                          ))}
                          {getEconomicEvents(day).length > 2 && (
                            <div className="text-[10px] text-amber-400/80 text-left">+{getEconomicEvents(day).length - 2} more</div>
                          )}
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        )}

        {calendarView === "day" && (
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="text-center">
                <div className="text-lg font-medium">{format(currentMonth, "EEEE, MMMM d, yyyy")}</div>
                <div className="text-sm text-muted-foreground">
                  {formatDistanceStrict(currentMonth, new Date(), { addSuffix: true })}
                </div>
              </div>

              {/* Day type indicators */}
              <div className="flex gap-2">
                {isWeekend(currentMonth) && (
                  <Badge variant="outline" className="bg-muted/30">
                    Weekend
                  </Badge>
                )}
                {hasEconomicEvents(currentMonth) && (
                  <Badge variant="outline" className="text-amber-500 border-amber-500/50">
                    {getEconomicEvents(currentMonth).length} Economic Events
                  </Badge>
                )}
              </div>
            </div>

            {/* Trading sessions visualization */}
            {showTradingSessions && !isWeekend(currentMonth) && (
              <Card className="p-4">
                <div className="text-sm font-medium mb-2">Trading Sessions (UTC)</div>
                <div className="relative h-8 bg-muted/30 rounded-md overflow-hidden">
                  {TRADING_SESSIONS.map((session) => (
                    <div
                      key={session.name}
                      className={cn(
                        "absolute h-full flex items-center justify-center text-xs font-medium",
                        session.color
                      )}
                      style={{
                        left: `${(session.startHour / 24) * 100}%`,
                        width: `${((session.endHour - session.startHour) / 24) * 100}%`,
                      }}
                    >
                      {session.name}
                    </div>
                  ))}
                  {/* Hour markers */}
                  {Array.from({ length: 24 }).map((_, i) => (
                    <div
                      key={i}
                      className="absolute h-full w-px bg-border"
                      style={{ left: `${(i / 24) * 100}%` }}
                    >
                      <div className="absolute -top-5 -translate-x-1/2 text-[10px] text-muted-foreground">
                        {i}:00
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Economic events */}
            {showEconomicEvents && hasEconomicEvents(currentMonth) && (
              <Card className="p-4">
                <div className="text-sm font-medium mb-2">Economic Events</div>
                <div className="space-y-2">
                  {getEconomicEvents(currentMonth).map((event, i) => (
                    <div key={i} className="flex items-start p-2 rounded-md bg-muted/30">
                      <div className="mr-2 mt-0.5">
                        <AlertCircleIcon className={cn(
                          "h-4 w-4",
                          event.impact === 'high' ? "text-rose-500" :
                          event.impact === 'medium' ? "text-amber-500" :
                          "text-blue-500"
                        )} />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <div className="font-medium text-sm">{event.event}</div>
                          <div className="text-xs text-muted-foreground">{event.time}</div>
                        </div>
                        <div className="text-xs">
                          <span className="font-medium">{event.currency}</span>
                          {event.actual && (
                            <span className="ml-2">Actual: <span className="text-emerald-500">{event.actual}</span></span>
                          )}
                          {event.forecast && (
                            <span className="ml-2">Forecast: <span className="text-blue-500">{event.forecast}</span></span>
                          )}
                          {event.previous && (
                            <span className="ml-2">Previous: <span className="text-muted-foreground">{event.previous}</span></span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Strategy performance */}
            {showStrategyOverlay && selectedStrategy && hasStrategyTrades(currentMonth) && userId && (
              <Card className="p-4">
                <div className="text-sm font-medium mb-2">Strategy Performance</div>
                <StrategyDayPerformance
                  userId={userId}
                  strategyId={selectedStrategy.id}
                  date={currentMonth}
                  strategy={selectedStrategy}
                />
              </Card>
            )}

            {/* Performance Metrics */}
            {getDayMetrics(currentMonth) ? (
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                {(() => {
                  const metrics = getDayMetrics(currentMonth);
                  if (!metrics) return null;
                  return (
                    <>
                      <Card className="dashboard-card bg-card p-4">
                        <div className="text-sm font-medium text-muted-foreground">Total P&L</div>
                        <div className={cn(
                          "text-2xl font-bold",
                          metrics.isProfitable ? "text-emerald-500" : "text-rose-500"
                        )}>
                          {metrics.isProfitable ? `$${metrics.totalProfit.toFixed(2)}` : `$-${Math.abs(metrics.totalProfit).toFixed(2)}`}
                        </div>
                      </Card>
                      <Card className="dashboard-card bg-card p-4">
                        <div className="text-sm font-medium text-muted-foreground">Trades</div>
                        <div className="text-2xl font-bold">{metrics.tradeCount}</div>
                      </Card>
                      <Card className="dashboard-card bg-card p-4">
                        <div className="text-sm font-medium text-muted-foreground">Win/Loss</div>
                        <div className="text-2xl font-bold">
                          {metrics.winCount}/{metrics.lossCount}
                        </div>
                      </Card>
                      <Card className="dashboard-card bg-card p-4">
                        <div className="text-sm font-medium text-muted-foreground">Win Rate</div>
                        <div className="text-2xl font-bold">{metrics.winRate}</div>
                      </Card>
                    </>
                  );
                })()}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No trading activity on this day
              </div>
            )}

            <TradingDayNote
              date={currentMonth}
              initialNote={getNote(currentMonth)}
              onSave={saveNote}
            />

            {getDayTrades(currentMonth).length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Trades</h3>
                <div className="border rounded-md overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-muted/50">
                        <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground">Symbol</th>
                        <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground">Type</th>
                        <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden xs:table-cell">Entry</th>
                        <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden xs:table-cell">Exit</th>
                        <th className="px-2 sm:px-4 py-2 text-right text-xs font-medium text-muted-foreground">P&L</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getDayTrades(currentMonth).map((trade: any, i: number) => (
                        <tr key={i} className="border-t hover:bg-muted/50">
                          <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm">{trade.symbol}</td>
                          <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm">{trade.type}</td>
                          <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden xs:table-cell">{format(new Date(trade.time_open), "HH:mm:ss")}</td>
                          <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden xs:table-cell">{format(new Date(trade.time_close), "HH:mm:ss")}</td>
                          <td className={cn(
                            "px-2 sm:px-4 py-2 text-xs sm:text-sm text-right font-medium",
                            trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                          )}>
                            {trade.profit >= 0 ? `$${trade.profit.toFixed(2)}` : `-$${Math.abs(trade.profit).toFixed(2)}`}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
