"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"
import { getUserAccounts, createAccount, updateAccount, deleteAccount, getAccountTradeCount } from "@/lib/trade-service"
import { useAccount } from "@/contexts/account-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertD<PERSON>og<PERSON>oot<PERSON>, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { PlusCircle, Wallet, Edit, Trash2, AlertTriangle } from "lucide-react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

// Define the form schema for adding a new account
const accountFormSchema = z.object({
  name: z.string().min(1, "Account name is required"),
  broker: z.string().min(1, "Broker name is required"),
})

type AccountFormValues = z.infer<typeof accountFormSchema>

interface TradingAccount {
  id: string
  name: string
  account_number: string
  broker: string
  created_at: string
  updated_at: string
}

export default function AccountsPage() {
  const [accounts, setAccounts] = useState<TradingAccount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAddingAccount, setIsAddingAccount] = useState(false)
  const [isSubmittingAccount, setIsSubmittingAccount] = useState(false)
  const [editingAccount, setEditingAccount] = useState<TradingAccount | null>(null)
  const [isUpdatingAccount, setIsUpdatingAccount] = useState(false)
  const [deletingAccount, setDeletingAccount] = useState<TradingAccount | null>(null)
  const [isDeletingAccount, setIsDeletingAccount] = useState(false)
  const [tradeCount, setTradeCount] = useState<number>(0)
  const [isLoadingTradeCount, setIsLoadingTradeCount] = useState(false)
  const router = useRouter()
  const supabase = getSupabaseBrowser()
  const { selectedAccountId, setSelectedAccountId, refreshAccounts } = useAccount()

  // Initialize form for adding accounts
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      name: "",
      broker: "",
    },
  })

  // Initialize form for editing accounts
  const editForm = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      name: "",
      broker: "",
    },
  })

  // Fetch user accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user?.id) {
          router.push('/auth')
          return
        }

        const accountsData = await getUserAccounts(user.id)
        setAccounts(accountsData)

        // Only auto-select the first account if there are accounts, none is selected,
        // and we don't have evidence that the user explicitly unselected an account
        const hasExplicitlyUnselected = localStorage.getItem('hasExplicitlyUnselected') === 'true'
        if (accountsData.length > 0 && !selectedAccountId && !hasExplicitlyUnselected) {
          setSelectedAccountId(accountsData[0].id)
        }
      } catch (error) {
        console.error("Error fetching accounts:", error)
        toast.error("Failed to load trading accounts")
      } finally {
        setIsLoading(false)
      }
    }

    fetchAccounts()
  }, [supabase.auth, router, selectedAccountId, setSelectedAccountId])

  // Handle account selection or unselection
  const handleSelectAccount = async (accountId: string) => {
    // If the account is already selected, unselect it
    if (selectedAccountId === accountId) {
      setSelectedAccountId(null)
      // Set a flag in localStorage to remember that the user explicitly unselected an account
      localStorage.setItem('hasExplicitlyUnselected', 'true')
      toast.success("Account unselected - now viewing data across all accounts")

      // Also save this preference to the database
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user?.id) {
          await supabase
            .from('user_preferences')
            .update({
              selected_account_id: null,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id)
        }
      } catch (error) {
        console.error('Error saving account unselection to database:', error)
      }
    } else {
      // Otherwise, select the account
      setSelectedAccountId(accountId)
      // Clear the flag since the user is now selecting an account
      localStorage.removeItem('hasExplicitlyUnselected')
      toast.success("Account selected")

      // Also save this preference to the database
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user?.id) {
          // Check if user_preferences record exists
          const { data: existingPrefs } = await supabase
            .from('user_preferences')
            .select('user_id')
            .eq('user_id', user.id)
            .maybeSingle()

          if (existingPrefs) {
            // Update existing preferences
            await supabase
              .from('user_preferences')
              .update({
                selected_account_id: accountId,
                updated_at: new Date().toISOString()
              })
              .eq('user_id', user.id)
          } else {
            // Create new preferences
            await supabase
              .from('user_preferences')
              .insert({
                user_id: user.id,
                selected_account_id: accountId,
                updated_at: new Date().toISOString()
              })
          }
        }
      } catch (error) {
        console.error('Error saving account selection to database:', error)
      }
    }
  }

  // Handle form submission for adding account
  const onSubmit = async (values: AccountFormValues) => {
    if (isSubmittingAccount) return // Prevent double submission

    setIsSubmittingAccount(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user?.id) {
        toast.error("Please sign in to add an account")
        return
      }

      // Generate a unique account number
      const accountNumber = `ACC-${Date.now()}`

      // Create the account
      const newAccount = await createAccount(user.id, {
        name: values.name,
        broker: values.broker,
        account_number: accountNumber,
      })

      // Optimistically add the new account to the local state immediately
      setAccounts(prev => [...prev, newAccount])

      // Reset form and close dialog immediately for smooth UX
      form.reset()
      setIsAddingAccount(false)

      // Show success message
      toast.success("Account added successfully")

      // Refresh the account context to update the AccountSwitcher
      console.log("Refreshing account context after account creation")
      refreshAccounts().catch(error => {
        console.error("Error refreshing account context after creation:", error)
      })

      // Refresh accounts list in the background to ensure consistency
      try {
        const accountsData = await getUserAccounts(user.id)
        setAccounts(accountsData)
      } catch (refreshError) {
        console.error("Error refreshing accounts after creation:", refreshError)
        // Don't show error to user since the account was created successfully
      }
    } catch (error) {
      console.error("Error adding account:", error)
      toast.error("Failed to add account")
    } finally {
      setIsSubmittingAccount(false)
    }
  }

  // Handle edit account
  const handleEditAccount = (account: TradingAccount) => {
    setEditingAccount(account)
    editForm.reset({
      name: account.name,
      broker: account.broker,
    })
  }

  // Handle form submission for editing account
  const onEditSubmit = async (values: AccountFormValues) => {
    if (!editingAccount || isUpdatingAccount) return

    setIsUpdatingAccount(true)
    try {
      await updateAccount(editingAccount.id, {
        name: values.name,
        broker: values.broker,
      })

      // Update accounts list optimistically
      setAccounts(prev => prev.map(acc =>
        acc.id === editingAccount.id
          ? { ...acc, name: values.name, broker: values.broker, updated_at: new Date().toISOString() }
          : acc
      ))

      // Refresh the account context to update the AccountSwitcher
      console.log("Refreshing account context after account update")
      refreshAccounts().catch(error => {
        console.error("Error refreshing account context after update:", error)
      })

      toast.success("Account updated successfully")
      setEditingAccount(null)
      editForm.reset()
    } catch (error) {
      console.error("Error updating account:", error)
      toast.error("Failed to update account")
    } finally {
      setIsUpdatingAccount(false)
    }
  }

  // Handle delete account preparation
  const handleDeleteAccount = async (account: TradingAccount) => {
    setDeletingAccount(account)
    setIsLoadingTradeCount(true)

    try {
      const count = await getAccountTradeCount(account.id)
      setTradeCount(count)
    } catch (error) {
      console.error("Error getting trade count:", error)
      setTradeCount(0)
    } finally {
      setIsLoadingTradeCount(false)
    }
  }

  // Handle delete account confirmation
  const confirmDeleteAccount = async (forceDelete: boolean = false) => {
    if (!deletingAccount || isDeletingAccount) return

    setIsDeletingAccount(true)
    try {
      console.log("Starting account deletion process for:", deletingAccount.id)

      // Delete the account from the database
      const result = await deleteAccount(deletingAccount.id, forceDelete)
      console.log("Account deletion result:", result)

      // If this was the selected account, clear selection first
      if (selectedAccountId === deletingAccount.id) {
        console.log("Clearing selected account as it was deleted")
        setSelectedAccountId(null)
      }

      // Remove account from local state
      setAccounts(prev => {
        const filtered = prev.filter(acc => acc.id !== deletingAccount.id)
        console.log("Updated local accounts count:", filtered.length)
        return filtered
      })

      // Refresh the account context to ensure consistency with a small delay to allow DB propagation
      console.log("Refreshing account context after deletion")
      setTimeout(() => {
        refreshAccounts().catch(error => {
          console.error("Error refreshing account context:", error)
        })
      }, 500) // 500ms delay to allow database changes to propagate

      toast.success("Account deleted successfully")
      setDeletingAccount(null)
      setTradeCount(0)
    } catch (error) {
      console.error("Error deleting account:", error)

      // Show more specific error message
      const errorMessage = error instanceof Error ? error.message : "Failed to delete account"
      toast.error(`Failed to delete account: ${errorMessage}`)

      // Refresh accounts list to ensure UI is consistent with database
      try {
        console.log("Refreshing accounts after failed deletion")
        const { data: { user } } = await supabase.auth.getUser()
        if (user?.id) {
          const accountsData = await getUserAccounts(user.id, true) // Use cache busting
          setAccounts(accountsData)
          console.log("Refreshed accounts count after error:", accountsData.length)
        }
      } catch (refreshError) {
        console.error("Error refreshing accounts after failed deletion:", refreshError)
      }
    } finally {
      setIsDeletingAccount(false)
    }
  }

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Trading Accounts</h1>
        <Dialog open={isAddingAccount} onOpenChange={setIsAddingAccount}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Account
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Trading Account</DialogTitle>
              <DialogDescription>
                Create a new trading account to organize your trades.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Main Account" {...field} />
                      </FormControl>
                      <FormDescription>
                        A descriptive name for your trading account
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="broker"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Broker</FormLabel>
                      <FormControl>
                        <Input placeholder="MetaTrader 5" {...field} />
                      </FormControl>
                      <FormDescription>
                        The broker or platform you use for trading
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={isSubmittingAccount}>
                    {isSubmittingAccount ? "Adding..." : "Add Account"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6">
        {isLoading ? (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-center h-40">
                <p className="text-muted-foreground">Loading accounts...</p>
              </div>
            </CardContent>
          </Card>
        ) : accounts.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center h-40 space-y-4">
                <Wallet className="h-12 w-12 text-muted-foreground" />
                <div className="text-center">
                  <h3 className="text-lg font-medium">No Trading Accounts</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    You haven&apos;t added any trading accounts yet. Add your first account to get started.
                  </p>
                </div>
                <Button onClick={() => setIsAddingAccount(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Account
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Your Trading Accounts</CardTitle>
              <CardDescription>
                Select an account to view and manage its trades.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account Name</TableHead>
                    <TableHead>Broker</TableHead>
                    <TableHead>Account Number</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {accounts.map((account) => (
                    <TableRow key={account.id} className={selectedAccountId === account.id ? "bg-accent" : ""}>
                      <TableCell className="font-medium">{account.name}</TableCell>
                      <TableCell>{account.broker}</TableCell>
                      <TableCell>{account.account_number}</TableCell>
                      <TableCell>{new Date(account.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                          <Button
                            variant={selectedAccountId === account.id ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleSelectAccount(account.id)}
                            className="text-xs sm:text-sm"
                          >
                            {selectedAccountId === account.id ? "Unselect" : "Select"}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAccount(account)}
                            className="h-8 w-8 p-0 hover:bg-accent"
                            title="Edit account"
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit account</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteAccount(account)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                            title="Delete account"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete account</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-muted-foreground">
                The selected account will be used for all trading operations. Unselect an account to view data across all accounts.
              </p>
            </CardFooter>
          </Card>
        )}
      </div>

      {/* Edit Account Dialog */}
      <Dialog open={!!editingAccount} onOpenChange={(open) => !open && setEditingAccount(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Trading Account</DialogTitle>
            <DialogDescription>
              Update your trading account information.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter account name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="broker"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Broker</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter broker name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setEditingAccount(null)} disabled={isUpdatingAccount}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isUpdatingAccount}>
                  {isUpdatingAccount ? "Updating..." : "Update Account"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Account Dialog */}
      <AlertDialog open={!!deletingAccount} onOpenChange={(open) => !open && setDeletingAccount(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Trading Account
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div>
                  Are you sure you want to delete the account <strong>&ldquo;{deletingAccount?.name}&rdquo;</strong>?
                </div>
                {isLoadingTradeCount ? (
                  <div className="text-muted-foreground">Checking for associated trades...</div>
                ) : tradeCount > 0 ? (
                  <div className="rounded-md bg-destructive/10 p-3 border border-destructive/20">
                    <div className="text-destructive font-medium">
                      ⚠️ This account has {tradeCount} associated trade{tradeCount !== 1 ? 's' : ''}.
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Deleting this account will permanently remove all associated trades, journal entries, and performance data. This action cannot be undone.
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">
                    This account has no associated trades and can be safely deleted.
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeletingAccount}>Cancel</AlertDialogCancel>
            {tradeCount > 0 ? (
              <AlertDialogAction
                onClick={() => confirmDeleteAccount(true)}
                disabled={isDeletingAccount}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeletingAccount ? "Deleting..." : "Delete Account & All Trades"}
              </AlertDialogAction>
            ) : (
              <AlertDialogAction
                onClick={() => confirmDeleteAccount(false)}
                disabled={isDeletingAccount}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeletingAccount ? "Deleting..." : "Delete Account"}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
