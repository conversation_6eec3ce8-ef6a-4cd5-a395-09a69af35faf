import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { format, startOfMonth, endOfMonth, addMonths, subMonths, eachDayOfInterval, getDay } from 'date-fns';

// Define cache configuration
export const revalidate = 0; // Disable caching to ensure fresh data on each request

// Helper function to get days to display for a month
function getDaysToDisplay(currentMonth: Date) {
  // Get the first day of the month
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);

  // Get all days in the month
  const daysInMonth = eachDayOfInterval({
    start: monthStart,
    end: monthEnd,
  });

  // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
  const firstDayOfWeek = getDay(monthStart);

  // Add days from the previous month to align with the correct day of week
  const prevMonthDays = Array.from({ length: firstDayOfWeek }, (_, i) => {
    // Create dates for the previous month days
    return new Date(monthStart.getFullYear(), monthStart.getMonth(), -firstDayOfWeek + i + 1);
  });

  // Calculate how many days we need from the next month to complete the grid
  // We want to ensure we have complete weeks (multiples of 7)
  const totalDaysSoFar = prevMonthDays.length + daysInMonth.length;
  const remainingDays = (Math.ceil(totalDaysSoFar / 7) * 7) - totalDaysSoFar;

  // Add days from the next month
  const nextMonthDays = Array.from({ length: remainingDays }, (_, i) => {
    // Create dates for the next month days
    return new Date(monthEnd.getFullYear(), monthEnd.getMonth(), monthEnd.getDate() + i + 1);
  });

  return [...prevMonthDays, ...daysInMonth, ...nextMonthDays];
}

// Helper function to calculate day metrics
function calculateDayMetrics(dayTrades: any[]) {
  if (dayTrades.length === 0) return null;

  const totalProfit = dayTrades.reduce((sum, trade) => sum + trade.profit, 0);
  const winningTrades = dayTrades.filter(trade => trade.profit > 0);
  const losingTrades = dayTrades.filter(trade => trade.profit < 0);
  const winRate = dayTrades.length > 0 ? (winningTrades.length / dayTrades.length) * 100 : 0;

  return {
    totalProfit,
    tradeCount: dayTrades.length,
    winCount: winningTrades.length,
    lossCount: losingTrades.length,
    winRate: winRate.toFixed(0) + '%',
    isProfitable: totalProfit > 0
  };
}

// Helper function to calculate weekly metrics
function calculateWeeklyMetrics(weekTrades: any[]) {
  if (weekTrades.length === 0) return null;

  const totalProfit = weekTrades.reduce((sum, trade) => sum + trade.profit, 0);
  const winningTrades = weekTrades.filter(trade => trade.profit > 0);
  const losingTrades = weekTrades.filter(trade => trade.profit < 0);
  const winRate = weekTrades.length > 0 ? (winningTrades.length / weekTrades.length) * 100 : 0;

  return {
    totalProfit,
    tradeCount: weekTrades.length,
    winCount: winningTrades.length,
    lossCount: losingTrades.length,
    winRate: winRate.toFixed(0) + '%',
    isProfitable: totalProfit > 0
  };
}

// GET handler to fetch calendar data
export async function GET(request: NextRequest) {
  try {
    // Get URL parameters
    const searchParams = request.nextUrl.searchParams;
    const year = searchParams.get('year');
    const month = searchParams.get('month');
    const accountId = searchParams.get('accountId');
    const view = searchParams.get('view') || 'month';

    // Parse the date
    let currentMonth: Date;
    if (year && month) {
      currentMonth = new Date(parseInt(year), parseInt(month), 1);
    } else {
      currentMonth = new Date();
      currentMonth.setDate(1); // First day of current month
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Calculate date range based on view
    let startDate, endDate;
    if (view === 'month') {
      // For month view, include previous and next month for complete calendar
      startDate = format(subMonths(startOfMonth(currentMonth), 1), 'yyyy-MM-dd');
      endDate = format(addMonths(endOfMonth(currentMonth), 1), 'yyyy-MM-dd');
    } else {
      // For other views, just use the current month
      startDate = format(startOfMonth(currentMonth), 'yyyy-MM-dd');
      endDate = format(endOfMonth(currentMonth), 'yyyy-MM-dd');
    }

    // Fetch trades for the date range
    let tradesQuery = supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .gte('time_close', startDate)
      .lte('time_close', endDate);

    if (accountId && accountId !== 'null') {
      tradesQuery = tradesQuery.eq('account_id', accountId);
    }

    const { data: trades, error: tradesError } = await tradesQuery;

    if (tradesError) {
      console.error('Error fetching trades:', tradesError);
      return NextResponse.json({ error: tradesError.message }, { status: 500 });
    }

    // Fetch trading notes
    let notesQuery = supabase
      .from('trading_notes')
      .select('*')
      .eq('user_id', userId);

    if (accountId && accountId !== 'null') {
      notesQuery = notesQuery.eq('account_id', accountId);
    }

    const { data: tradingNotes, error: notesError } = await notesQuery;

    if (notesError) {
      console.error('Error fetching trading notes:', notesError);
      return NextResponse.json({ error: notesError.message }, { status: 500 });
    }

    // Fetch journal entries
    let journalQuery = supabase
      .from('daily_journal_entries')
      .select('date, note, screenshots')
      .eq('user_id', userId)
      .gte('date', startDate)
      .lte('date', endDate);

    if (accountId && accountId !== 'null') {
      journalQuery = journalQuery.eq('account_id', accountId);
    }

    const { data: journalEntries, error: journalError } = await journalQuery;

    if (journalError) {
      console.error('Error fetching journal entries:', journalError);
      return NextResponse.json({ error: journalError.message }, { status: 500 });
    }

    // Process the data
    const daysToDisplay = getDaysToDisplay(currentMonth);
    
    // Group trades by date
    const tradesByDate: Record<string, any[]> = {};
    trades?.forEach(trade => {
      const dateKey = format(new Date(trade.time_close), 'yyyy-MM-dd');
      if (!tradesByDate[dateKey]) {
        tradesByDate[dateKey] = [];
      }
      tradesByDate[dateKey].push(trade);
    });

    // Calculate metrics for each day
    const dayMetrics: Record<string, any> = {};
    Object.entries(tradesByDate).forEach(([dateKey, dayTrades]) => {
      dayMetrics[dateKey] = calculateDayMetrics(dayTrades);
    });

    // Group days into weeks for weekly metrics
    const weeks = [];
    for (let i = 0; i < daysToDisplay.length; i += 7) {
      weeks.push(daysToDisplay.slice(i, i + 7));
    }

    // Calculate weekly metrics
    const weeklyMetrics = weeks.map(week => {
      // Filter only days from current month and get their trades
      const weekTrades = week
        .filter(day => day.getMonth() === currentMonth.getMonth())
        .flatMap(day => {
          const dateKey = format(day, 'yyyy-MM-dd');
          return tradesByDate[dateKey] || [];
        });

      return calculateWeeklyMetrics(weekTrades);
    });

    // Convert trading notes to a map
    const tradingNotesMap: Record<string, string> = {};
    tradingNotes?.forEach(note => {
      if (note.date && note.content) {
        tradingNotesMap[note.date] = note.content;
      }
    });

    // Convert journal entries to a map of dates with entries
    const datesWithJournalEntries = new Set<string>();
    journalEntries?.forEach(entry => {
      if (entry.date && (entry.note || (entry.screenshots && entry.screenshots.length > 0))) {
        datesWithJournalEntries.add(entry.date);
      }
    });

    // Return the processed data
    return NextResponse.json({
      currentMonth: format(currentMonth, 'yyyy-MM-dd'),
      daysToDisplay: daysToDisplay.map(day => format(day, 'yyyy-MM-dd')),
      tradesByDate,
      dayMetrics,
      weeklyMetrics,
      tradingNotes: tradingNotesMap,
      datesWithJournalEntries: Array.from(datesWithJournalEntries),
      weeks: weeks.map(week => week.map(day => format(day, 'yyyy-MM-dd')))
    });
  } catch (error) {
    console.error('Error in calendar-data API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
