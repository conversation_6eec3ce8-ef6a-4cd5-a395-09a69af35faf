"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"
import { CustomMetric, Goal } from "@/types/metrics"
import { Trade } from "@/types/trade"
import { getCustomMetrics, getGoals } from "@/lib/metrics-service"
import { getTrades } from "@/lib/trade-service"
import { useAccount } from "@/contexts/account-context"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Target, TrendingUp } from "lucide-react"

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CustomMetricsList } from "@/components/metrics-goals/custom-metrics-list"
import { CustomMetricForm } from "@/components/metrics-goals/custom-metric-form"
import { GoalsList } from "@/components/metrics-goals/goals-list"
import { GoalForm } from "@/components/metrics-goals/goal-form"

export default function MetricsGoalsPage() {
  const router = useRouter()
  const supabase = getSupabaseBrowser()
  const { selectedAccountId } = useAccount()

  const [userId, setUserId] = useState<string | null>(null)
  const [metrics, setMetrics] = useState<CustomMetric[]>([])
  const [goals, setGoals] = useState<Goal[]>([])
  const [trades, setTrades] = useState<Trade[]>([])
  const [activeTab, setActiveTab] = useState<"metrics" | "goals">("metrics")
  const [showMetricForm, setShowMetricForm] = useState(false)
  const [showGoalForm, setShowGoalForm] = useState(false)
  const [editingMetric, setEditingMetric] = useState<CustomMetric | null>(null)
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Get the current user ID
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        setUserId(user.id)
      } else {
        router.push("/auth")
      }
    }

    getUser()
  }, [supabase, router])

  // Load metrics, goals, and trades when user ID is available
  useEffect(() => {
    if (!userId) return

    const loadData = async () => {
      setIsLoading(true)
      try {
        // Load metrics
        const metricsData = await getCustomMetrics(userId)
        setMetrics(metricsData)

        // Load goals
        const goalsData = await getGoals(userId)
        setGoals(goalsData)

        // Load trades for metric calculations - filter by selected account
        const tradesData = await getTrades(userId, selectedAccountId)
        setTrades(tradesData)
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Failed to load metrics and goals")
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [userId, selectedAccountId])

  // Handle adding a new metric
  const handleAddMetric = () => {
    setEditingMetric(null)
    setShowMetricForm(true)
    setActiveTab("metrics")
  }

  // Handle editing a metric
  const handleEditMetric = (metric: CustomMetric) => {
    setEditingMetric(metric)
    setShowMetricForm(true)
    setActiveTab("metrics")
  }

  // Handle metric form submission
  const handleMetricSuccess = (metric: CustomMetric) => {
    if (editingMetric) {
      // Update existing metric in the list
      setMetrics(metrics.map(m => m.id === metric.id ? metric : m))
    } else {
      // Add new metric to the list
      setMetrics([metric, ...metrics])
    }

    setShowMetricForm(false)
    setEditingMetric(null)
  }

  // Handle metric deletion
  const handleDeleteMetric = (metricId: string) => {
    setMetrics(metrics.filter(m => m.id !== metricId))
  }

  // Handle adding a new goal
  const handleAddGoal = () => {
    setEditingGoal(null)
    setShowGoalForm(true)
    setActiveTab("goals")
  }

  // Handle editing a goal
  const handleEditGoal = (goal: Goal) => {
    setEditingGoal(goal)
    setShowGoalForm(true)
    setActiveTab("goals")
  }

  // Handle goal form submission
  const handleGoalSuccess = (goal: Goal) => {
    if (editingGoal) {
      // Update existing goal in the list
      setGoals(goals.map(g => g.id === goal.id ? goal : g))
    } else {
      // Add new goal to the list
      setGoals([goal, ...goals])
    }

    setShowGoalForm(false)
    setEditingGoal(null)
  }

  // Handle goal deletion
  const handleDeleteGoal = (goalId: string) => {
    setGoals(goals.filter(g => g.id !== goalId))
  }

  if (!userId) {
    return <div className="container py-10">Loading...</div>
  }

  if (selectedAccountId === null) {
    return (
      <div className="container py-10">
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your metrics and goals. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-10">
      <div className="flex items-center justify-end mb-6">
        {activeTab === "metrics" && !showMetricForm && (
          <Button onClick={handleAddMetric}>
            <TrendingUp className="mr-2 h-4 w-4" />
            Add Metric
          </Button>
        )}

        {activeTab === "goals" && !showGoalForm && (
          <Button onClick={handleAddGoal}>
            <Target className="mr-2 h-4 w-4" />
            Add Goal
          </Button>
        )}
      </div>

      <p className="text-muted-foreground mb-8">
        Create custom metrics to track your trading performance and set goals to improve your results.
      </p>

      <Tabs defaultValue="metrics" value={activeTab} onValueChange={(value) => setActiveTab(value as "metrics" | "goals")}>
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger value="metrics">
            <TrendingUp className="mr-2 h-4 w-4" />
            Custom Metrics
          </TabsTrigger>
          <TabsTrigger value="goals">
            <Target className="mr-2 h-4 w-4" />
            Trading Goals
          </TabsTrigger>
        </TabsList>

        <TabsContent value="metrics">
          {showMetricForm ? (
            <CustomMetricForm
              userId={userId}
              metric={editingMetric || undefined}
              onSuccess={handleMetricSuccess}
              onCancel={() => {
                setShowMetricForm(false)
                setEditingMetric(null)
              }}
            />
          ) : (
            <CustomMetricsList
              userId={userId}
              metrics={metrics}
              onEdit={handleEditMetric}
              onDelete={handleDeleteMetric}
              onAdd={handleAddMetric}
            />
          )}
        </TabsContent>

        <TabsContent value="goals">
          {showGoalForm ? (
            <GoalForm
              userId={userId}
              goal={editingGoal || undefined}
              metrics={metrics}
              onSuccess={handleGoalSuccess}
              onCancel={() => {
                setShowGoalForm(false)
                setEditingGoal(null)
              }}
            />
          ) : (
            <GoalsList
              userId={userId}
              goals={goals}
              metrics={metrics}
              onEdit={handleEditGoal}
              onDelete={handleDeleteGoal}
              onAdd={handleAddGoal}
            />
          )}
        </TabsContent>
      </Tabs>

      <Separator className="my-10" />

      <div className="space-y-6">
        <h2 className="text-2xl font-bold tracking-tight">How It Works</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Custom Metrics</h3>
            <p className="text-muted-foreground">
              Create personalized metrics to track specific aspects of your trading performance.
            </p>
            <ul className="space-y-2 list-disc pl-5">
              <li>Define formulas using trading variables like win rate, profit, etc.</li>
              <li>Set target values to track your progress</li>
              <li>Visualize your metrics on the dashboard</li>
              <li>Use metrics to identify strengths and weaknesses</li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Trading Goals</h3>
            <p className="text-muted-foreground">
              Set specific, measurable goals to improve your trading performance over time.
            </p>
            <ul className="space-y-2 list-disc pl-5">
              <li>Link goals to custom metrics for automatic tracking</li>
              <li>Set target dates to stay accountable</li>
              <li>Track your progress visually</li>
              <li>Celebrate achievements when you reach your goals</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
