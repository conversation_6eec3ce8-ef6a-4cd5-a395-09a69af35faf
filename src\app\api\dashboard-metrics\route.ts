import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// Define cache configuration
export const revalidate = 0; // Disable caching to ensure fresh data on each request

// GET handler to fetch dashboard metrics
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'metrics'; // 'metrics' or 'goals'
    const accountId = url.searchParams.get('accountId');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // If accountId is explicitly null (user has unselected all accounts), return empty array
    if (accountId === 'null') {
      console.log('No account selected, returning empty metrics/goals array');
      return NextResponse.json([]);
    }

    if (type === 'metrics') {
      // Fetch custom metrics
      let query = supabase
        .from('custom_metrics')
        .select('*')
        .eq('user_id', userId);

      // Note: We're not filtering by account_id for metrics since the column doesn't exist
      // Custom metrics are user-specific, not account-specific

      // Execute query with ordering
      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching custom metrics:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data || []);
    } else if (type === 'goals') {
      // Fetch goals
      let query = supabase
        .from('goals')
        .select('*, custom_metrics(*)')
        .eq('user_id', userId);

      // Check if the goals table has an account_id column before filtering
      try {
        // Add account filter if provided
        if (accountId) {
          // First check if the column exists
          const { data: columnInfo, error: columnError } = await supabase
            .from('goals')
            .select('account_id')
            .limit(1);

          // If no error, the column exists and we can filter by it
          if (!columnError) {
            query = query.eq('account_id', accountId);
          } else {
            console.log('Note: goals table does not have account_id column, skipping filter');
          }
        }
      } catch (err) {
        console.log('Error checking goals table schema:', err);
        // Continue without the filter if there's an error
      }

      // Execute query with ordering
      const { data, error } = await query.order('end_date', { ascending: true });

      if (error) {
        console.error('Error fetching goals:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data || []);
    } else {
      return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in dashboard-metrics API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
