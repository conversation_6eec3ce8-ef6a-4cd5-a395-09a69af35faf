"use client"

import { useState, useMemo } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell,
  LineChart, Line, Area, AreaChart, PieChart, Pie
} from "recharts"
import { 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Clock, 
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Filter,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { Trade } from "@/types/trade"
import { useAccount } from "@/contexts/account-context"
import { useAnalyticsData } from "@/hooks/use-analytics-data"
import { cn } from "@/lib/utils"
import { format, subDays, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay } from "date-fns"

interface ModernAnalyticsProps {
  initialTrades: Trade[]
  initialSummary: any | null
}

interface StatItemProps {
  label: string
  value: string | number
  trend?: "positive" | "negative" | "neutral"
  format?: "currency" | "percentage" | "number" | "days"
}

function StatItem({ label, value, trend = "neutral", format = "number" }: StatItemProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === "string") return val
    
    switch (format) {
      case "currency":
        return `$${val.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
      case "percentage":
        return `${val.toFixed(2)}%`
      case "days":
        return `${val} ${val === 1 ? 'day' : 'days'}`
      default:
        return val.toLocaleString()
    }
  }

  const getTrendColor = () => {
    switch (trend) {
      case "positive":
        return "text-emerald-600"
      case "negative":
        return "text-rose-600"
      default:
        return "text-foreground"
    }
  }

  return (
    <div className="flex justify-between items-center py-2">
      <span className="text-sm text-muted-foreground">{label}</span>
      <span className={cn("text-sm font-medium tabular-nums", getTrendColor())}>
        {formatValue(value)}
      </span>
    </div>
  )
}

function StatsSection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div className="space-y-1">
      <h3 className="text-sm font-medium text-foreground mb-3">{title}</h3>
      <div className="space-y-1">
        {children}
      </div>
    </div>
  )
}

export default function ModernAnalytics({ initialTrades, initialSummary }: ModernAnalyticsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [timeRange, setTimeRange] = useState("all")
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const { selectedAccountId } = useAccount()

  const {
    data: analyticsData,
    isLoading
  } = useAnalyticsData(selectedAccountId)

  const trades = analyticsData?.trades || initialTrades
  const initialBalance = analyticsData?.summary?.initial_balance || initialSummary?.initial_balance || 10000

  // Calculate comprehensive statistics
  const stats = useMemo(() => {
    if (!trades || trades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalPnL: 0,
        bestMonth: 0,
        worstMonth: 0,
        averageMonth: 0,
        averageDailyVolume: 0,
        averageWinningTrade: 0,
        averageLosingTrade: 0,
        averageHoldTime: 0,
        totalTradingDays: 0,
        winningDays: 0,
        losingDays: 0,
        profitFactor: 0,
        largestWin: 0,
        largestLoss: 0,
        maxConsecutiveWins: 0,
        maxConsecutiveLosses: 0,
        totalCommissions: 0,
        totalSwap: 0,
        netProfit: 0,
        grossProfit: 0,
        grossLoss: 0
      }
    }

    const totalTrades = trades.length
    const winningTrades = trades.filter(t => t.profit > 0).length
    const losingTrades = trades.filter(t => t.profit < 0).length
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0

    const totalPnL = trades.reduce((sum, t) => sum + t.profit, 0)
    const grossProfit = trades.filter(t => t.profit > 0).reduce((sum, t) => sum + t.profit, 0)
    const grossLoss = Math.abs(trades.filter(t => t.profit < 0).reduce((sum, t) => sum + t.profit, 0))
    
    const averageWinningTrade = winningTrades > 0 ? grossProfit / winningTrades : 0
    const averageLosingTrade = losingTrades > 0 ? grossLoss / losingTrades : 0
    
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0
    
    const largestWin = Math.max(...trades.map(t => t.profit))
    const largestLoss = Math.min(...trades.map(t => t.profit))

    // Group trades by month for monthly statistics
    const monthlyPnL = new Map<string, number>()
    trades.forEach(trade => {
      const monthKey = format(new Date(trade.time_close), 'yyyy-MM')
      monthlyPnL.set(monthKey, (monthlyPnL.get(monthKey) || 0) + trade.profit)
    })

    const monthlyValues = Array.from(monthlyPnL.values())
    const bestMonth = monthlyValues.length > 0 ? Math.max(...monthlyValues) : 0
    const worstMonth = monthlyValues.length > 0 ? Math.min(...monthlyValues) : 0
    const averageMonth = monthlyValues.length > 0 ? monthlyValues.reduce((sum, val) => sum + val, 0) / monthlyValues.length : 0

    // Calculate trading days
    const tradingDays = new Set(trades.map(t => format(new Date(trade.time_close), 'yyyy-MM-dd')))
    const totalTradingDays = tradingDays.size

    // Group by day for daily statistics
    const dailyPnL = new Map<string, number>()
    trades.forEach(trade => {
      const dayKey = format(new Date(trade.time_close), 'yyyy-MM-dd')
      dailyPnL.set(dayKey, (dailyPnL.get(dayKey) || 0) + trade.profit)
    })

    const winningDays = Array.from(dailyPnL.values()).filter(pnl => pnl > 0).length
    const losingDays = Array.from(dailyPnL.values()).filter(pnl => pnl < 0).length

    const averageDailyVolume = totalTradingDays > 0 ? trades.reduce((sum, t) => sum + t.volume, 0) / totalTradingDays : 0

    return {
      totalTrades,
      winningTrades,
      losingTrades,
      winRate,
      totalPnL,
      bestMonth,
      worstMonth,
      averageMonth,
      averageDailyVolume,
      averageWinningTrade,
      averageLosingTrade,
      averageHoldTime: 0, // TODO: Calculate from trade duration
      totalTradingDays,
      winningDays,
      losingDays,
      profitFactor,
      largestWin,
      largestLoss,
      maxConsecutiveWins: 0, // TODO: Calculate streaks
      maxConsecutiveLosses: 0,
      totalCommissions: trades.reduce((sum, t) => sum + (t.commission || 0), 0),
      totalSwap: trades.reduce((sum, t) => sum + (t.swap || 0), 0),
      netProfit: totalPnL,
      grossProfit,
      grossLoss
    }
  }, [trades])

  // Prepare chart data
  const dailyCumulativePnL = useMemo(() => {
    if (!trades || trades.length === 0) return []

    const sortedTrades = [...trades].sort((a, b) => 
      new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    )

    let cumulativePnL = initialBalance
    const data: { date: string; value: number; dailyPnL: number }[] = []

    // Group trades by day
    const dailyTrades = new Map<string, Trade[]>()
    sortedTrades.forEach(trade => {
      const dateKey = format(new Date(trade.time_close), 'yyyy-MM-dd')
      if (!dailyTrades.has(dateKey)) {
        dailyTrades.set(dateKey, [])
      }
      dailyTrades.get(dateKey)!.push(trade)
    })

    // Calculate cumulative P&L
    Array.from(dailyTrades.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([date, dayTrades]) => {
        const dailyPnL = dayTrades.reduce((sum, t) => sum + t.profit, 0)
        cumulativePnL += dailyPnL
        data.push({
          date,
          value: cumulativePnL,
          dailyPnL
        })
      })

    return data
  }, [trades, initialBalance])

  if (isLoading) {
    return (
      <div className="container py-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-64" />
          <div className="h-10 bg-muted rounded w-full" />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="h-96 bg-muted rounded" />
            <div className="h-96 bg-muted rounded" />
            <div className="h-96 bg-muted rounded" />
          </div>
        </div>
      </div>
    )
  }

  if (!selectedAccountId) {
    return (
      <div className="container py-6">
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            <BarChart3 className="h-12 w-12 text-muted-foreground" />
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground">
              Please select an account to view analytics data.
            </p>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive trading performance analysis
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="ytd">Year to Date</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="detailed">Detailed</TabsTrigger>
          <TabsTrigger value="options">Options</TabsTrigger>
          <TabsTrigger value="risk">Risk</TabsTrigger>
          <TabsTrigger value="wins-losses">Wins vs Losses</TabsTrigger>
          <TabsTrigger value="compare">Compare</TabsTrigger>
          <TabsTrigger value="market-behavior">Market Behavior</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Your Stats - Left Column */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>Your Stats</span>
                  <Badge variant="outline" className="ml-auto">All Dates</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <StatsSection title="Performance Overview">
                  <StatItem
                    label="Best Month"
                    value={stats.bestMonth}
                    format="currency"
                    trend={stats.bestMonth > 0 ? "positive" : "neutral"}
                  />
                  <StatItem
                    label="Lowest Month"
                    value={stats.worstMonth}
                    format="currency"
                    trend={stats.worstMonth < 0 ? "negative" : "neutral"}
                  />
                  <StatItem
                    label="Average"
                    value={stats.averageMonth}
                    format="currency"
                    trend={stats.averageMonth > 0 ? "positive" : stats.averageMonth < 0 ? "negative" : "neutral"}
                  />
                </StatsSection>

                <Separator />

                <StatsSection title="Trading Metrics">
                  <StatItem
                    label="Total P&L"
                    value={stats.totalPnL}
                    format="currency"
                    trend={stats.totalPnL > 0 ? "positive" : stats.totalPnL < 0 ? "negative" : "neutral"}
                  />
                  <StatItem
                    label="Average Daily Volume"
                    value={stats.averageDailyVolume}
                    format="number"
                  />
                  <StatItem
                    label="Average Winning Trade"
                    value={stats.averageWinningTrade}
                    format="currency"
                    trend="positive"
                  />
                  <StatItem
                    label="Average Losing Trade"
                    value={stats.averageLosingTrade}
                    format="currency"
                    trend="negative"
                  />
                  <StatItem
                    label="Total Number of Trades"
                    value={stats.totalTrades}
                    format="number"
                  />
                  <StatItem
                    label="Number of Winning Trades"
                    value={stats.winningTrades}
                    format="number"
                    trend="positive"
                  />
                  <StatItem
                    label="Number of Losing Trades"
                    value={stats.losingTrades}
                    format="number"
                    trend="negative"
                  />
                </StatsSection>
              </CardContent>
            </Card>

            {/* Additional Stats - Right Column */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>Additional Metrics</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <StatsSection title="Time Analysis">
                  <StatItem
                    label="Average Hold Time (Losing Trades)"
                    value="N/A"
                    format="number"
                  />
                  <StatItem
                    label="Average Hold Time (Scratch Trades)"
                    value="N/A"
                    format="number"
                  />
                  <StatItem
                    label="Average Trade P&L"
                    value={stats.totalTrades > 0 ? stats.totalPnL / stats.totalTrades : 0}
                    format="currency"
                    trend={stats.totalPnL > 0 ? "positive" : stats.totalPnL < 0 ? "negative" : "neutral"}
                  />
                </StatsSection>

                <Separator />

                <StatsSection title="Trading Activity">
                  <StatItem
                    label="Profit Factor"
                    value={stats.profitFactor === Infinity ? "∞" : stats.profitFactor.toFixed(2)}
                    format="number"
                    trend={stats.profitFactor > 1 ? "positive" : "negative"}
                  />
                  <StatItem
                    label="Open Trades"
                    value={0}
                    format="number"
                  />
                  <StatItem
                    label="Total Trading Days"
                    value={stats.totalTradingDays}
                    format="days"
                  />
                  <StatItem
                    label="Winning Days"
                    value={stats.winningDays}
                    format="days"
                    trend="positive"
                  />
                  <StatItem
                    label="Losing Days"
                    value={stats.losingDays}
                    format="days"
                    trend="negative"
                  />
                  <StatItem
                    label="Breakeven Days"
                    value={stats.totalTradingDays - stats.winningDays - stats.losingDays}
                    format="days"
                  />
                  <StatItem
                    label="Logged Days"
                    value={stats.totalTradingDays}
                    format="days"
                  />
                </StatsSection>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Performance Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <StatsSection title="Key Performance">
                  <StatItem
                    label="Largest Profit"
                    value={stats.largestWin}
                    format="currency"
                    trend="positive"
                  />
                  <StatItem
                    label="Largest Loss"
                    value={Math.abs(stats.largestLoss)}
                    format="currency"
                    trend="negative"
                  />
                  <StatItem
                    label="Average Realized R-Multiple"
                    value="N/A"
                    format="number"
                  />
                  <StatItem
                    label="Average Planned R-Multiple"
                    value="N/A"
                    format="number"
                  />
                  <StatItem
                    label="Trade Expectancy"
                    value={stats.totalTrades > 0 ? (stats.totalPnL / stats.totalTrades).toFixed(2) : "0"}
                    format="currency"
                    trend={stats.totalPnL > 0 ? "positive" : stats.totalPnL < 0 ? "negative" : "neutral"}
                  />
                </StatsSection>

                <Separator />

                <StatsSection title="Win Rate Analysis">
                  <StatItem
                    label="Win Rate"
                    value={stats.winRate}
                    format="percentage"
                    trend={stats.winRate >= 50 ? "positive" : "negative"}
                  />
                  <StatItem
                    label="Max Consecutive Winning Days"
                    value="N/A"
                    format="number"
                  />
                  <StatItem
                    label="Max Consecutive Losing Days"
                    value="N/A"
                    format="number"
                  />
                  <StatItem
                    label="Average Daily P&L"
                    value={stats.totalTradingDays > 0 ? stats.totalPnL / stats.totalTradingDays : 0}
                    format="currency"
                    trend={stats.totalPnL > 0 ? "positive" : stats.totalPnL < 0 ? "negative" : "neutral"}
                  />
                </StatsSection>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Daily Net Cumulative P&L Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Daily Net Cumulative P&L</span>
                  <Badge variant="outline" className="ml-auto">All Dates</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={dailyCumulativePnL}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => `$${value.toLocaleString()}`}
                      />
                      <Tooltip
                        formatter={(value: number) => [`$${value.toLocaleString()}`, 'Balance']}
                        labelFormatter={(label) => format(new Date(label), 'MMM dd, yyyy')}
                      />
                      <Area
                        type="monotone"
                        dataKey="value"
                        stroke="hsl(var(--primary))"
                        fill="hsl(var(--primary))"
                        fillOpacity={0.1}
                        strokeWidth={2}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Net Daily P&L Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Net Daily P&L</span>
                  <Badge variant="outline" className="ml-auto">All Dates</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={dailyCumulativePnL}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => `$${value.toLocaleString()}`}
                      />
                      <Tooltip
                        formatter={(value: number, name) => [
                          `$${value.toLocaleString()}`,
                          'Daily P&L'
                        ]}
                        labelFormatter={(label) => format(new Date(label), 'MMM dd, yyyy')}
                      />
                      <Bar dataKey="dailyPnL">
                        {dailyCumulativePnL.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.dailyPnL >= 0 ? "hsl(var(--chart-2))" : "hsl(var(--chart-1))"}
                          />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Risk Tab */}
        <TabsContent value="risk" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Trade Distribution by R-Multiple</CardTitle>
                <p className="text-sm text-muted-foreground">All Dates</p>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>R-Multiple data not available</p>
                      <p className="text-sm">Configure risk parameters to view this chart</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance by R-Multiple</CardTitle>
                <p className="text-sm text-muted-foreground">All Dates</p>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>R-Multiple performance data not available</p>
                      <p className="text-sm">Configure risk parameters to view this chart</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Summary Table */}
          <Card>
            <CardHeader>
              <CardTitle>Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 font-medium">R-Multiple</th>
                      <th className="text-right py-2 font-medium">Net Profit</th>
                      <th className="text-right py-2 font-medium">Winning %</th>
                      <th className="text-right py-2 font-medium">Total Profit</th>
                      <th className="text-right py-2 font-medium">Total Loss</th>
                      <th className="text-right py-2 font-medium">Trades</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="py-2">None</td>
                      <td className="text-right py-2 tabular-nums">${stats.totalPnL.toLocaleString()}</td>
                      <td className="text-right py-2 tabular-nums">{stats.winRate.toFixed(1)}%</td>
                      <td className="text-right py-2 tabular-nums text-emerald-600">${stats.grossProfit.toLocaleString()}</td>
                      <td className="text-right py-2 tabular-nums text-rose-600">${stats.grossLoss.toLocaleString()}</td>
                      <td className="text-right py-2 tabular-nums">{stats.totalTrades}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Market Behavior Tab */}
        <TabsContent value="market-behavior" className="space-y-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Filter by:</span>
              <Badge variant="secondary">Market Movement</Badge>
              <Badge variant="secondary">Market Opening Gap</Badge>
              <Badge variant="secondary">Market Day Type</Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Distribution by Movement in Percent</CardTitle>
                <p className="text-sm text-muted-foreground">All Dates</p>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Market movement data not available</p>
                      <p className="text-sm">Import market data to view this analysis</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance by Movement in Percent</CardTitle>
                <p className="text-sm text-muted-foreground">All Dates</p>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Market performance data not available</p>
                      <p className="text-sm">Import market data to view this analysis</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Calendar Tab */}
        <TabsContent value="calendar" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Year</h2>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedYear(prev => prev - 1)}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-lg font-medium px-4">{selectedYear}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedYear(prev => prev + 1)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Array.from({ length: 12 }, (_, i) => {
              const month = i
              const monthStart = startOfMonth(new Date(selectedYear, month))
              const monthEnd = endOfMonth(new Date(selectedYear, month))
              const monthName = format(monthStart, 'MMMM')

              return (
                <Card key={month} className="p-4">
                  <h3 className="font-medium text-center mb-3">{monthName}</h3>
                  <div className="grid grid-cols-7 gap-1 text-xs">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                      <div key={day} className="text-center p-1 font-medium text-muted-foreground">
                        {day}
                      </div>
                    ))}
                    {eachDayOfInterval({ start: monthStart, end: monthEnd }).map(date => {
                      // Find trades for this date
                      const dayTrades = trades.filter(trade =>
                        isSameDay(new Date(trade.time_close), date)
                      )
                      const dayPnL = dayTrades.reduce((sum, trade) => sum + trade.profit, 0)

                      let bgColor = ""
                      if (dayPnL > 0) bgColor = "bg-emerald-100 text-emerald-800"
                      else if (dayPnL < 0) bgColor = "bg-rose-100 text-rose-800"
                      else if (dayTrades.length > 0) bgColor = "bg-gray-100 text-gray-800"

                      return (
                        <div
                          key={date.toISOString()}
                          className={cn(
                            "text-center p-1 rounded text-xs cursor-pointer hover:bg-muted",
                            bgColor
                          )}
                          title={dayTrades.length > 0 ? `${dayTrades.length} trades, P&L: $${dayPnL.toFixed(2)}` : ''}
                        >
                          {format(date, 'd')}
                        </div>
                      )
                    })}
                  </div>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        {/* Placeholder tabs */}
        <TabsContent value="detailed">
          <Card className="p-8 text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Detailed Analytics</h3>
            <p className="text-muted-foreground">Coming soon - Advanced detailed analytics and breakdowns</p>
          </Card>
        </TabsContent>

        <TabsContent value="options">
          <Card className="p-8 text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Options Analytics</h3>
            <p className="text-muted-foreground">Coming soon - Options-specific trading analytics</p>
          </Card>
        </TabsContent>

        <TabsContent value="wins-losses">
          <Card className="p-8 text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Wins vs Losses</h3>
            <p className="text-muted-foreground">Coming soon - Detailed win/loss analysis</p>
          </Card>
        </TabsContent>

        <TabsContent value="compare">
          <Card className="p-8 text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Compare</h3>
            <p className="text-muted-foreground">Coming soon - Performance comparison tools</p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
