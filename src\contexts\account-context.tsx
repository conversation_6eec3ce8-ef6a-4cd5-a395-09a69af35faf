"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { getSupabaseBrowser } from "@/lib/supabase-browser"
import { getUserAccounts, getSelectedAccountId, setSelectedAccountId } from "@/lib/data/account-data"
import { useToast } from "@/components/ui/toast-provider"
import { handleError } from "@/lib/error-handler"

interface TradingAccount {
  id: string
  name: string | null
  account_number: string
  broker: string | null
  created_at: string
  updated_at: string
}

interface AccountContextType {
  accounts: TradingAccount[]
  selectedAccountId: string | null
  setSelectedAccountId: (id: string | null) => void
  refreshAccounts: () => Promise<void>
  isLoading: boolean
  isAccountSwitching: boolean
  isInitializing: boolean // New flag to track initial account loading
  error: string | null
}

const AccountContext = createContext<AccountContextType | undefined>(undefined)

export function AccountProvider({ children }: { children: ReactNode }) {
  const [accounts, setAccounts] = useState<TradingAccount[]>([])
  const [selectedAccountId, setSelectedAccountIdState] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAccountSwitching, setIsAccountSwitching] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true) // Track initialization state
  const [error, setError] = useState<string | null>(null)
  const supabase = getSupabaseBrowser()
  const { showErrorToast } = useToast()

  // This effect runs once on component mount to initialize the selected account
  useEffect(() => {
    const initializeSelectedAccount = async () => {
      try {
        setIsInitializing(true) // Start initialization
        console.log('=== ACCOUNT CONTEXT INITIALIZATION ===')

        // IMPORTANT: We're going to prioritize Supabase data from the very beginning
        // We'll only check localStorage as a last resort

        // Check if we have a user session
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
          console.log('No user logged in')
          setIsInitializing(false)
          return
        }

        console.log('Current user:', user.id)

        // OPTIMIZATION: Fetch accounts and selected account ID in parallel
        // This ensures we get the database data as quickly as possible
        console.log('Fetching account data from Supabase...')
        const [dbAccountId, accountsData] = await Promise.all([
          getSelectedAccountId(user.id),
          getUserAccounts(user.id)
        ]);

        // Update accounts state immediately
        setAccounts(accountsData);

        // Log Supabase data first - this is our primary source of truth
        if (dbAccountId) {
          console.log('Found account ID in Supabase:', dbAccountId)
        } else {
          console.log('No account ID found in Supabase')
        }

        // Only check localStorage as a fallback
        // We'll log this after the Supabase data to emphasize it's secondary
        const storedAccountId = localStorage.getItem('selectedAccountId')
        const hasExplicitlyUnselected = localStorage.getItem('hasExplicitlyUnselected') === 'true'

        console.log('Fallback - storedAccountId from localStorage:', storedAccountId)
        console.log('Fallback - hasExplicitlyUnselected:', hasExplicitlyUnselected)

        // PRIORITY ORDER:
        // 1. Valid Supabase account ID
        // 2. Valid localStorage account ID (if not explicitly unselected)
        // 3. First account in the list (if available and not explicitly unselected)
        // 4. No account selected

        // First, check if we have a valid Supabase account ID
        if (dbAccountId) {
          // Check if the account exists in the accounts list
          if (accountsData.some(account => account.id === dbAccountId)) {
            console.log('Setting initial selectedAccountId from Supabase:', dbAccountId)
            setSelectedAccountIdState(dbAccountId)
            // Also update localStorage to keep them in sync
            localStorage.setItem('selectedAccountId', dbAccountId)
            // Ensure the explicit unselection flag is removed
            localStorage.removeItem('hasExplicitlyUnselected')
          } else {
            console.log('Supabase account ID not found in accounts list')
            // Clear the invalid account ID
            await setSelectedAccountId(user.id, null)
            setSelectedAccountIdState(null)
            localStorage.removeItem('selectedAccountId')

            // Fall back to first account if available
            if (accountsData.length > 0 && !hasExplicitlyUnselected) {
              console.log('Falling back to first account:', accountsData[0].id)
              setSelectedAccountIdState(accountsData[0].id)
              localStorage.setItem('selectedAccountId', accountsData[0].id)
              // Save to database for future use
              await setSelectedAccountId(user.id, accountsData[0].id)
            }
          }
        }
        // If no valid Supabase account ID, check localStorage as fallback
        else if (storedAccountId && !hasExplicitlyUnselected) {
          // Check if the account exists in the accounts list
          if (accountsData.some(account => account.id === storedAccountId)) {
            console.log('Setting initial selectedAccountId from localStorage fallback:', storedAccountId)
            setSelectedAccountIdState(storedAccountId)
            // Save to database for future use
            await setSelectedAccountId(user.id, storedAccountId)
          } else {
            console.log('localStorage account ID not found in accounts list')
            setSelectedAccountIdState(null)
            localStorage.removeItem('selectedAccountId')

            // Fall back to first account if available
            if (accountsData.length > 0 && !hasExplicitlyUnselected) {
              console.log('Falling back to first account:', accountsData[0].id)
              setSelectedAccountIdState(accountsData[0].id)
              localStorage.setItem('selectedAccountId', accountsData[0].id)
              // Save to database for future use
              await setSelectedAccountId(user.id, accountsData[0].id)
            }
          }
        }
        // If no valid account ID from any source, try to use the first account
        else if (accountsData.length > 0 && !hasExplicitlyUnselected) {
          console.log('No account selected from any source, selecting first account:', accountsData[0].id)
          setSelectedAccountIdState(accountsData[0].id)
          localStorage.setItem('selectedAccountId', accountsData[0].id)
          // Save to database for future use
          await setSelectedAccountId(user.id, accountsData[0].id)
        }
        // If all else fails, don't select any account
        else {
          console.log('Not setting initial selectedAccountId - no valid account found or explicitly unselected')
          setSelectedAccountIdState(null)
          localStorage.removeItem('selectedAccountId')
        }

        // Important: Set initializing to false here to ensure the account ID is set before components use it
        setIsInitializing(false)
        console.log('=== END INITIALIZATION ===')
      } catch (error) {
        console.error('Error during account initialization:', error)
        showErrorToast(error as Error, 'Error initializing account selection')
        // Make sure to set initializing to false even if there's an error
        setIsInitializing(false)
      }
    }

    initializeSelectedAccount()

    // We've already fetched accounts in the initialization function
    // This is just a backup in case initialization fails
    const fetchAccountsIfNeeded = async () => {
      // Only fetch accounts if we don't have any yet (initialization might have failed)
      if (accounts.length === 0 && !isInitializing) {
        try {
          console.log('No accounts loaded yet, fetching as backup...');
          // Don't set isLoading here since this is a background operation
          // and shouldn't affect the UI loading state
          setError(null)

          const { data: { user } } = await supabase.auth.getUser()
          if (!user?.id) {
            return
          }

          // Get accounts using our standardized data access layer
          const accountsData = await getUserAccounts(user.id)
          setAccounts(accountsData)

          console.log(`Backup fetch completed, found ${accountsData.length} accounts`);
        } catch (error) {
          console.error("Error in backup account fetch:", error)
          setError("Failed to load trading accounts")
          showErrorToast(error as Error, "Failed to load trading accounts")
        }
      } else {
        console.log('Accounts already loaded or initialization in progress, skipping backup fetch');
      }
    }

    // Wait a short time to see if initialization completes
    const timer = setTimeout(() => {
      fetchAccountsIfNeeded();
    }, 2000); // 2 second delay

    return () => clearTimeout(timer);
  }, [supabase.auth])

  // Function to refresh accounts from the database
  const refreshAccounts = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user?.id) {
        return
      }

      console.log('Account Context: Refreshing accounts from database')
      const accountsData = await getUserAccounts(user.id)
      setAccounts(accountsData)

      // If the currently selected account no longer exists, clear selection
      if (selectedAccountId && !accountsData.find(acc => acc.id === selectedAccountId)) {
        console.log('Account Context: Selected account no longer exists, clearing selection')
        setSelectedAccountIdState(null)
      }
    } catch (error) {
      console.error('Error refreshing accounts:', error)
      setError(error instanceof Error ? error.message : 'Failed to refresh accounts')
    }
  }

  // Custom function to set the selected account ID and trigger loading state
  const handleSetSelectedAccountId = (id: string | null) => {
    // Only set loading state if we're changing accounts
    if (id !== selectedAccountId) {
      console.log('Account Context: Setting account switching state to true')
      setIsAccountSwitching(true)

      // Clear any cached data for the previous account
      // This will be handled by the individual components

      // If explicitly setting to null, mark that the user has explicitly unselected
      if (id === null) {
        localStorage.setItem('hasExplicitlyUnselected', 'true')
      } else {
        // If selecting an account, remove the explicit unselection flag
        localStorage.removeItem('hasExplicitlyUnselected')
      }

      setSelectedAccountIdState(id)

      // Also save to user_preferences if the user is logged in
      const saveToPreferences = async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (user?.id) {
            console.log('Saving account selection to preferences:', id)
            await setSelectedAccountId(user.id, id)
          }
        } catch (error) {
          console.error('Error saving account selection to preferences:', error)
          showErrorToast(error as Error, 'Failed to save account selection')
        }
      }

      saveToPreferences()
    } else {
      console.log('Account Context: Account already selected, no change needed')
    }
  }

  // Update localStorage when selected account changes
  useEffect(() => {
    console.log('Selected account changed:', selectedAccountId)

    if (selectedAccountId) {
      localStorage.setItem('selectedAccountId', selectedAccountId)
      console.log('Saved selectedAccountId to localStorage:', selectedAccountId)
    } else {
      // Remove from localStorage if account is unselected
      localStorage.removeItem('selectedAccountId')
      console.log('Removed selectedAccountId from localStorage')
    }

    // Reset the account switching state after a short delay
    // This gives components time to show loading state
    const timer = setTimeout(() => {
      setIsAccountSwitching(false)
    }, 500) // Short delay to ensure loading state is visible

    return () => clearTimeout(timer)
  }, [selectedAccountId])

  // Add a listener to refresh accounts when the window gets focus or visibility changes
  useEffect(() => {
    // Track last refresh time to prevent too frequent refreshes
    let lastRefreshTime = Date.now()
    let tabHiddenTime: number | null = null
    const REFRESH_THRESHOLD = 5000 // 5 seconds between refreshes
    const AWAY_THRESHOLD = 180000   // 3 minutes away to trigger refresh

    // Function to fetch accounts without setting loading states that affect UI
    const fetchAccountsWithoutInit = async () => {
      try {
        // Don't set isLoading here since this is a background refresh
        // and shouldn't affect the UI loading state
        setError(null)

        const { data: { user } } = await supabase.auth.getUser()
        if (!user?.id) {
          return
        }

        const accountsData = await getUserAccounts(user.id)
        setAccounts(accountsData)
      } catch (err) {
        console.error("Error refreshing accounts:", err)
      }
    }

    const handleFocus = () => {
      // We'll disable the focus handler since it's causing unwanted refreshes
      // The visibility change handler will take care of refreshes
      console.log('Account Context: Window focused event - refresh disabled to prevent unwanted refreshes')
      // No refresh happens here anymore
    }

    // Handle visibility changes with time away tracking
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // User left the tab - record the time
        tabHiddenTime = Date.now()
        console.log(`Tab hidden at: ${new Date(tabHiddenTime).toLocaleTimeString()}`)
      }
      else if (document.visibilityState === 'visible') {
        // User returned to the tab
        const now = Date.now()

        if (tabHiddenTime) {
          const timeAway = now - tabHiddenTime
          console.log(`Tab visible again. Was hidden for ${Math.round(timeAway/1000)} seconds. Threshold is ${AWAY_THRESHOLD/1000} seconds.`)

          // Only refresh if they've been away for longer than AWAY_THRESHOLD
          // AND enough time has passed since last refresh
          if (timeAway > AWAY_THRESHOLD && (now - lastRefreshTime > REFRESH_THRESHOLD)) {
            console.log(`Tab was hidden for ${Math.round(timeAway/1000)} seconds (>3 min), refreshing accounts...`)
            lastRefreshTime = now
            fetchAccountsWithoutInit()
          } else {
            console.log(`Tab was hidden for ${Math.round(timeAway/1000)} seconds, skipping account refresh (below 3 min threshold)`)
          }
        } else {
          console.log('Tab became visible but no hidden time was recorded')
        }

        // Reset the hidden time
        tabHiddenTime = null
      }
    }

    window.addEventListener('focus', handleFocus)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('focus', handleFocus)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [supabase.auth])

  return (
    <AccountContext.Provider
      value={{
        accounts,
        selectedAccountId,
        setSelectedAccountId: handleSetSelectedAccountId,
        refreshAccounts,
        isLoading,
        isAccountSwitching,
        isInitializing,
        error
      }}
    >
      {children}
    </AccountContext.Provider>
  )
}

export function useAccount() {
  const context = useContext(AccountContext)
  if (context === undefined) {
    throw new Error("useAccount must be used within an AccountProvider")
  }
  return context
}
